
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdarg.h>
#include <ctype.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include "libconfig/libconfig.h"
#include "manage.h"

/*
 * @description     : ,拆分字符串
 * @param - *src    : 原字符串
 * @param - *dest   : 拆分后的字符串数组
 * @return	        : 正常返回获得几个字符串，未找到返回0
 */
int func_split_string(char *src, struct_class_list *dest)
{
    char *token;
    char *rest = src;
    int i = 0;

    while ((token = strtok_r(rest, ",", &rest)) != NULL)
    {
        strcpy(dest->strs[i], token);
        if (*rest == ',')
            i++;
        i++;
        if (i >= MAX_CLASS)
            break;
    }
    return i;
}
/*
 * @description     : 将获取的字符串指针拷贝到目标字符串数组中
 * @param - cfg     : 测试结构体
 * @param - *key    : key值
 * @param - *dest   : 目标字符串数组
 * @return	        : 0没找到，1找到
 */
int my_config_lookup_string(config_t *cfg, const char *key, char *dest)
{
    const char *strs = NULL;
    if (config_lookup_string(cfg, key, &strs))
    {
        strcpy(dest, strs);
        return 1;
    }
    return 0;
}
/*
 * @description     : 将获取的字符串指针拷贝到目标字符串数组中
 * @param - setting     : 设置结构体
 * @param - *name    : name
 * @param - *dest   : 目标字符串数组
 * @return	        : 0没找到，1找到
 */

int my_config_setting_lookup_string(const config_setting_t *setting,
                                    const char *name, char *dest)
{
    const char *strs = NULL;
    if (config_setting_lookup_string(setting, name, &strs))
    {
        strcpy(dest, strs);
        return 1;
    }
    return 0;
}

/*
 * @description        : 获取并解析参数配置文件
 * @param - *manag     : 测试管理结构体
 * @param - *file_name : 配置文件名
 * @return	           : 正常返回0，未找到返回-1
 */
int func_get_param(struct_test *manag, char *file_name)
{
    int ret = -1;

    config_t cfg;
    config_setting_t *setting;
    char class_name[500] = {0};
    char class_en[500] = {0};
    struct_class_list class_list, class_en_list;
    int class_name_count = 0, class_en_count = 0, i = 0, j = 0, k = 0;
    int value = 0;
    char temp_str[50] = {0};
    char temp_str1[50] = {0};
    int count = 0;
    char *temp_point = NULL;
    int temp_item = 0;

    config_init(&cfg);

    /* Read the file. If there is an error, report it and exit. */
    if (!config_read_file(&cfg, file_name))
    {
        fprintf(stderr, "%s:%d - %s\n", config_error_file(&cfg),
                config_error_line(&cfg), config_error_text(&cfg));
        config_destroy(&cfg);
        return ret;
    }

    /* Get the class_en name. */
    if (my_config_lookup_string(&cfg, "class_en", class_en))
        // printf("Store class_en: %s\n\n", class_en);
        ;
    else
        fprintf(stderr, "No 'class_en' setting in param.cfg file.\n");
    class_en_count = func_split_string(class_en, &class_en_list);

    /* Get the class name. */
    if (my_config_lookup_string(&cfg, "class_name", class_name))
        // printf("Store class_name: %s\n\n", class_name);
        ;
    else
        fprintf(stderr, "No 'class_name' setting in param.cfg file.\n");
    class_name_count = func_split_string(class_name, &class_list);
    if (class_name_count != class_en_count)
    {
        fprintf(stderr, "class_name and class_en count is not equal\n");
        config_destroy(&cfg);
        return -1;
    }
    manag->class_count = class_name_count;
    // 获取哪些模块需要测试
    for (i = 0; i < class_en_count; i++)
    {
        if (config_lookup_int(&cfg, &class_en_list.strs[i][0], &value))
        {
            manag->test_class[i]->class_en = value;
        }
        else
        {
            manag->test_class[i]->class_en = 0;
        }
    }
    // 获取功能，扩展功能，性能，稳定性哪个区域需要测试
    for (i = 0; i < class_name_count; i++)
    {
        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "%s.func_en", class_list.strs[i]);
        if (config_lookup_int(&cfg, temp_str, &value))
        {
            manag->test_class[i]->function.region_en = value;
        }
        else
        {
            manag->test_class[i]->function.region_en = 0;
        }

        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "%s.exp_func_en", class_list.strs[i]);
        if (config_lookup_int(&cfg, temp_str, &value))
        {
            manag->test_class[i]->exp_function.region_en = value;
        }
        else
        {
            manag->test_class[i]->exp_function.region_en = 0;
        }

        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "%s.perf_en", class_list.strs[i]);
        if (config_lookup_int(&cfg, temp_str, &value))
        {
            manag->test_class[i]->performance.region_en = value;
        }
        else
        {
            manag->test_class[i]->performance.region_en = 0;
        }

        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "%s.stab_en", class_list.strs[i]);
        if (config_lookup_int(&cfg, temp_str, &value))
        {
            manag->test_class[i]->stab.region_en = value;
        }
        else
        {
            manag->test_class[i]->stab.region_en = 0;
        }
    }
    for (i = 0; i < class_name_count; i++)
    {
        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "%s.function", class_list.strs[i]);
        setting = config_lookup(&cfg, temp_str);
        if (setting != NULL)
        {
            count = config_setting_length(setting);
            if (count > MAX_ITEM)
                count = MAX_ITEM;
            // manag->class[i].function.item_count = count;
            for (j = 0; j < count; ++j)
            {
                config_setting_t *function = config_setting_get_elem(setting, j);
                if (my_config_setting_lookup_string(function, "title", temp_str1) == 0)
                    continue;
                if (strstr(temp_str1, "-F-") != NULL)
                {
                    temp_point = strstr(temp_str1, "-F-");
                    temp_item = atoi(temp_point + strlen("-F-"));
                }
                else
                    continue;
                if (temp_item > MAX_ITEM)
                    continue;
                if (temp_item <= 0)
                    continue;
                if (temp_item > manag->test_class[i]->function.item_count)
                    manag->test_class[i]->function.item_count = temp_item;
                temp_item--;

                strcpy(manag->test_class[i]->function.item[temp_item].title, temp_str1);
                if (config_setting_lookup_int(function, "on_off", &value))
                {
                    manag->test_class[i]->function.item[temp_item].item_en = value;
                    // printf("class[%d].function.item[%d] title=%s en=%d\n", i, temp_item, manag->test_class[i]->function.item[temp_item].title, manag->test_class[i]->function.item[temp_item].item_en);
                }
                else
                {
                    manag->test_class[i]->function.item[temp_item].item_en = 0;
                }
                if (config_setting_lookup_int(function, "times", &value))
                {
                    manag->test_class[i]->function.item[temp_item].times = value;
                }
                else
                {
                    manag->test_class[i]->function.item[temp_item].times = 1;
                }
                if (config_setting_lookup_int(function, "dura", &value))
                {
                    manag->test_class[i]->function.item[temp_item].dura = value * 1000;
                }
                else
                {
                    manag->test_class[i]->function.item[temp_item].dura = 10;
                }
                if (config_setting_lookup_int(function, "timeout", &value))
                {
                    manag->test_class[i]->function.item[temp_item].timeout = value * 1000;
                }
                else
                {
                    manag->test_class[i]->function.item[temp_item].timeout = 60000;
                }
                if (config_setting_lookup_int(function, "para_numb", &value))
                {
                    if (value > PARAM_NUMB)
                        value = PARAM_NUMB;

                    manag->test_class[i]->function.item[temp_item].param_count = value;
                    if (value > 0)
                    {
                        for (k = 0; k < value; k++)
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "type%d", (k + 1));
                            if (config_setting_lookup_int(function, temp_str, (int *)&(manag->test_class[i]->function.item[temp_item].param[k].type)) == 0)
                                continue;
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "param%d", (k + 1));

                            if (manag->test_class[i]->function.item[temp_item].param[k].type == DATA_INT)
                            {
                                if (config_setting_lookup_int(function, temp_str, &value))
                                {
                                    manag->test_class[i]->function.item[temp_item].param[k].data_int = value;
                                }
                            }
                            else if (manag->test_class[i]->function.item[temp_item].param[k].type == DATA_STR)
                            {
                                my_config_setting_lookup_string(function, temp_str, manag->test_class[i]->function.item[temp_item].param[k].data_str);
                            }
                            else if (manag->test_class[i]->function.item[temp_item].param[k].type == DATA_FLOAT)
                            {
                                config_setting_lookup_float(function, temp_str, (double *)&(manag->test_class[i]->function.item[temp_item].param[k].data_float));
                            }
                        }
                    }
                }
                else
                {
                    manag->test_class[i]->function.item[temp_item].param_count = 0;
                }
            }
        }
        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "%s.exp_function", class_list.strs[i]);
        setting = config_lookup(&cfg, temp_str);
        if (setting != NULL)
        {
            count = config_setting_length(setting);
            if (count > MAX_ITEM1)
                count = MAX_ITEM1;
            // manag->class[i].exp_function.item_count = count;
            memset(temp_str, 0, sizeof(temp_str));
            for (j = 0; j < count; ++j)
            {
                config_setting_t *function = config_setting_get_elem(setting, j);
                if (my_config_setting_lookup_string(function, "title", temp_str1) == 0)
                    continue;
                if (strstr(temp_str1, "-EF-") != NULL)
                {
                    temp_point = strstr(temp_str1, "-EF-");
                    temp_item = atoi(temp_point + strlen("-EF-"));
                }
                else
                    continue;
                if (temp_item > MAX_ITEM1)
                    continue;
                if (temp_item <= 0)
                    continue;

                if (temp_item > manag->test_class[i]->exp_function.item_count)
                    manag->test_class[i]->exp_function.item_count = temp_item;
                temp_item--;
                strcpy(manag->test_class[i]->exp_function.item[temp_item].title, temp_str1);
                if (config_setting_lookup_int(function, "on_off", &value))
                {
                    manag->test_class[i]->exp_function.item[temp_item].item_en = value;
                }
                else
                {
                    manag->test_class[i]->exp_function.item[temp_item].item_en = 0;
                }
                if (config_setting_lookup_int(function, "times", &value))
                {
                    manag->test_class[i]->exp_function.item[temp_item].times = value;
                }
                else
                {
                    manag->test_class[i]->exp_function.item[temp_item].times = 1;
                }
                if (config_setting_lookup_int(function, "dura", &value))
                {
                    manag->test_class[i]->exp_function.item[temp_item].dura = value * 1000;
                }
                else
                {
                    manag->test_class[i]->exp_function.item[temp_item].dura = 10;
                }
                if (config_setting_lookup_int(function, "timeout", &value))
                {
                    manag->test_class[i]->exp_function.item[temp_item].timeout = value * 1000;
                }
                else
                {
                    manag->test_class[i]->exp_function.item[temp_item].timeout = 60000;
                }
                if (config_setting_lookup_int(function, "para_numb", &value))
                {
                    if (value > PARAM_NUMB)
                        value = PARAM_NUMB;

                    manag->test_class[i]->exp_function.item[temp_item].param_count = value;
                    if (value > 0)
                    {
                        for (k = 0; k < value; k++)
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "type%d", (k + 1));
                            if (config_setting_lookup_int(function, temp_str, (int *)&(manag->test_class[i]->exp_function.item[temp_item].param[k].type)) == 0)
                                continue;
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "param%d", (k + 1));

                            if (manag->test_class[i]->exp_function.item[temp_item].param[k].type == DATA_INT)
                            {
                                if (config_setting_lookup_int(function, temp_str, &value))
                                {
                                    manag->test_class[i]->exp_function.item[temp_item].param[k].data_int = value;
                                }
                            }
                            else if (manag->test_class[i]->exp_function.item[temp_item].param[k].type == DATA_STR)
                            {
                                my_config_setting_lookup_string(function, temp_str, manag->test_class[i]->exp_function.item[temp_item].param[k].data_str);
                            }
                            else if (manag->test_class[i]->exp_function.item[temp_item].param[k].type == DATA_FLOAT)
                            {
                                config_setting_lookup_float(function, temp_str, (double *)&(manag->test_class[i]->exp_function.item[temp_item].param[k].data_float));
                            }
                        }
                    }
                }
                else
                {
                    manag->test_class[i]->exp_function.item[temp_item].param_count = 0;
                }
            }
        }
        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "%s.performance", class_list.strs[i]);
        setting = config_lookup(&cfg, temp_str);
        if (setting != NULL)
        {
            count = config_setting_length(setting);
            if (count > MAX_ITEM1)
                count = MAX_ITEM1;
            // manag->class[i].performance.item_count = count;
            memset(temp_str, 0, sizeof(temp_str));
            for (j = 0; j < count; ++j)
            {
                config_setting_t *function = config_setting_get_elem(setting, j);
                if (my_config_setting_lookup_string(function, "title", temp_str1) == 0)
                    continue;
                if (strstr(temp_str1, "-PF-") != NULL)
                {
                    temp_point = strstr(temp_str1, "-PF-");
                    temp_item = atoi(temp_point + strlen("-PF-"));
                }
                else
                    continue;
                if (temp_item > MAX_ITEM1)
                    continue;
                if (temp_item <= 0)
                    continue;

                if (temp_item > manag->test_class[i]->performance.item_count)
                    manag->test_class[i]->performance.item_count = temp_item;
                temp_item--;
                strcpy(manag->test_class[i]->performance.item[temp_item].title, temp_str1);

                if (config_setting_lookup_int(function, "on_off", &value))
                {
                    manag->test_class[i]->performance.item[temp_item].item_en = value;
                }
                else
                {
                    manag->test_class[i]->performance.item[temp_item].item_en = 0;
                }
                if (config_setting_lookup_int(function, "times", &value))
                {
                    manag->test_class[i]->performance.item[temp_item].times = value;
                }
                else
                {
                    manag->test_class[i]->performance.item[temp_item].times = 1;
                }
                if (config_setting_lookup_int(function, "dura", &value))
                {
                    manag->test_class[i]->performance.item[temp_item].dura = value * 1000;
                }
                else
                {
                    manag->test_class[i]->performance.item[temp_item].dura = 10;
                }
                if (config_setting_lookup_int(function, "timeout", &value))
                {
                    manag->test_class[i]->performance.item[temp_item].timeout = value * 1000;
                }
                else
                {
                    manag->test_class[i]->performance.item[temp_item].timeout = 60000;
                }
                if (config_setting_lookup_int(function, "para_numb", &value))
                {
                    if (value > PARAM_NUMB)
                        value = PARAM_NUMB;

                    manag->test_class[i]->performance.item[temp_item].param_count = value;
                    if (value > 0)
                    {
                        for (k = 0; k < value; k++)
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "type%d", (k + 1));
                            if (config_setting_lookup_int(function, temp_str, (int *)&(manag->test_class[i]->performance.item[temp_item].param[k].type)) == 0)
                                continue;
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "param%d", (k + 1));

                            if (manag->test_class[i]->performance.item[temp_item].param[k].type == DATA_INT)
                            {
                                if (config_setting_lookup_int(function, temp_str, &value))
                                {
                                    manag->test_class[i]->performance.item[temp_item].param[k].data_int = value;
                                }
                            }
                            else if (manag->test_class[i]->performance.item[temp_item].param[k].type == DATA_STR)
                            {
                                my_config_setting_lookup_string(function, temp_str, manag->test_class[i]->performance.item[temp_item].param[k].data_str);
                            }
                            else if (manag->test_class[i]->performance.item[temp_item].param[k].type == DATA_FLOAT)
                            {
                                config_setting_lookup_float(function, temp_str, (double *)&(manag->test_class[i]->performance.item[temp_item].param[k].data_float));
                            }
                        }
                    }
                }
                else
                {
                    manag->test_class[i]->performance.item[temp_item].param_count = 0;
                }
            }
        }
        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "%s.stability", class_list.strs[i]);
        setting = config_lookup(&cfg, temp_str);
        if (setting != NULL)
        {
            count = config_setting_length(setting);
            if (count > MAX_ITEM1)
                count = MAX_ITEM1;
            // manag->class[i].stab.item_count = count;
            memset(temp_str, 0, sizeof(temp_str));
            for (j = 0; j < count; ++j)
            {
                config_setting_t *function = config_setting_get_elem(setting, j);
                if (my_config_setting_lookup_string(function, "title", temp_str1) == 0)
                    continue;
                if (strstr(temp_str1, "-S-") != NULL)
                {
                    temp_point = strstr(temp_str1, "-S-");
                    temp_item = atoi(temp_point + strlen("-S-"));
                }
                else
                    continue;
                if (temp_item > MAX_ITEM1)
                    continue;
                if (temp_item <= 0)
                    continue;

                if (temp_item > manag->test_class[i]->stab.item_count)
                    manag->test_class[i]->stab.item_count = temp_item;
                temp_item--;
                strcpy(manag->test_class[i]->stab.item[temp_item].title, temp_str1);

                if (config_setting_lookup_int(function, "on_off", &value))
                {
                    manag->test_class[i]->stab.item[temp_item].item_en = value;
                }
                else
                {
                    manag->test_class[i]->stab.item[temp_item].item_en = 0;
                }
                if (config_setting_lookup_int(function, "times", &value))
                {
                    manag->test_class[i]->stab.item[temp_item].times = value;
                }
                else
                {
                    manag->test_class[i]->stab.item[temp_item].times = 1;
                }
                if (config_setting_lookup_int(function, "dura", &value))
                {
                    manag->test_class[i]->stab.item[temp_item].dura = value * 1000;
                }
                else
                {
                    manag->test_class[i]->stab.item[temp_item].dura = 10;
                }
                if (config_setting_lookup_int(function, "timeout", &value))
                {
                    manag->test_class[i]->stab.item[temp_item].timeout = value * 1000;
                }
                else
                {
                    manag->test_class[i]->stab.item[temp_item].timeout = 60000;
                }
                if (config_setting_lookup_int(function, "para_numb", &value))
                {
                    if (value > PARAM_NUMB)
                        value = PARAM_NUMB;

                    manag->test_class[i]->stab.item[temp_item].param_count = value;
                    if (value > 0)
                    {
                        for (k = 0; k < value; k++)
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "type%d", (k + 1));
                            if (config_setting_lookup_int(function, temp_str, (int *)&(manag->test_class[i]->stab.item[temp_item].param[k].type)) == 0)
                                continue;
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "param%d", (k + 1));

                            if (manag->test_class[i]->stab.item[temp_item].param[k].type == DATA_INT)
                            {
                                if (config_setting_lookup_int(function, temp_str, &value))
                                {
                                    manag->test_class[i]->stab.item[temp_item].param[k].data_int = value;
                                }
                            }
                            else if (manag->test_class[i]->stab.item[temp_item].param[k].type == DATA_STR)
                            {
                                my_config_setting_lookup_string(function, temp_str, manag->test_class[i]->stab.item[temp_item].param[k].data_str);
                            }
                            else if (manag->test_class[i]->stab.item[temp_item].param[k].type == DATA_FLOAT)
                            {
                                config_setting_lookup_float(function, temp_str, (double *)&(manag->test_class[i]->stab.item[temp_item].param[k].data_float));
                            }
                        }
                    }
                }
                else
                {
                    manag->test_class[i]->stab.item[temp_item].param_count = 0;
                }
            }
        }
    }
    config_destroy(&cfg);
    return 0;
}
/*
 * @description        : 通过title获取对应的索引
 * @param - *manag     : 测试管理结构体
 * @param - *title     : 测试用例代码
 * @param - *item      : 该条测试项指针
 * @return	           : 正常返回0，异常返回-1
 */
struct_test_item *bsp_title_to_index(struct_test *manag, char *title, struct_class_list *list, int numb)
{
    int i = 0;
    int temp_class = 0, temp_item = 0;
    char *temp_point = NULL;

    for (i = 0; i < numb; i++)
    {
        if ((strstr(list->strs[i], "CAN") != NULL) && (strstr(list->strs[i], "CANFD") == NULL))
        {
            if (strstr(title, "CAN-") != NULL)
            {
                temp_class = i;
                break;
            }
        }
        else
        {
            if (strstr(title, list->strs[i]) != NULL)
            {
                temp_class = i;
                break;
            }
        }
    }
    if (i >= numb)
        return NULL;

    if (strstr(title, "-F-") != NULL)
    {
        temp_point = strstr(title, "-F-");
        temp_item = atoi(temp_point + strlen("-F-"));
        if (temp_item == 0)
            return NULL;
        temp_item -= 1;
        return (&(manag->test_class[temp_class]->function.item[temp_item]));
    }
    else if (strstr(title, "-EF-") != NULL)
    {
        temp_point = strstr(title, "-EF-");
        temp_item = atoi(temp_point + strlen("-EF-"));
        if (temp_item == 0)
            return NULL;

        temp_item -= 1;
        return (&(manag->test_class[temp_class]->exp_function.item[temp_item]));
    }
    else if (strstr(title, "-PF-") != NULL)
    {
        temp_point = strstr(title, "-PF-");
        temp_item = atoi(temp_point + strlen("-PF-"));
        if (temp_item == 0)
            return NULL;

        temp_item -= 1;
        return (&(manag->test_class[temp_class]->performance.item[temp_item]));
    }
    else if (strstr(title, "-S-") != NULL)
    {
        temp_point = strstr(title, "-S-");
        temp_item = atoi(temp_point + strlen("-S-"));
        if (temp_item == 0)
            return NULL;

        temp_item -= 1;
        return (&(manag->test_class[temp_class]->stab.item[temp_item]));
    }
    else
    {
        return NULL;
    }
    return NULL;
}
/*
 * @description        : 通过字符串获取get_value对应格式
 * @param - *form      : 格式字符串
 * @param - *dest      : 结构体
 * @return	           : 无
 */
void bsp_get_value_str_to_form(char *form, struct_get_value_form *dest)
{
    struct_class_list data;
    // int count = 0;

    // count = func_split_string(form, &data);
    func_split_string(form, &data);
    strcpy(dest->key_word, data.strs[0]);
    dest->modle = atoi(data.strs[1]);
    dest->x = atoi(data.strs[2]);
    dest->y = atoi(data.strs[3]);
    if ((dest->modle >= KEYWORD2I) && (dest->modle <= KEYWORD2_STR_ALL_SC_ERR))
    {
        strcpy(dest->key_word2, data.strs[4]);
        strcpy(dest->err_word, data.strs[5]);
        strcpy(dest->storage, data.strs[6]);
    }
    else
    {
        strcpy(dest->err_word, data.strs[4]);
        strcpy(dest->storage, data.strs[5]);
    }
}
/*
 * @description        : 通过字符串获取get_result对应格式
 * @param - *form      : 格式字符串
 * @param - *dest      : 结构体
 * @return	           : 无
 */
void bsp_get_result_str_to_form(char *form, struct_get_result_form *dest)
{
    struct_class_list data;
    int i = 0;

    func_split_string(form, &data);
    strcpy(dest->key_word, data.strs[0]);
    dest->modle = atoi(data.strs[1]);
    dest->kw_count = atoi(data.strs[2]);
    dest->ew_count = atoi(data.strs[3]);
    if (dest->kw_count != 0)
    {
        for (i = 0; i < dest->kw_count; i++)
        {
            strcpy(dest->pass_word[i], data.strs[i + 4]);
            // printf("pass_word[%d]: %s\n", i, dest->pass_word[i]);
        }
    }
    if (dest->ew_count != 0)
    {
        for (i = 0; i < dest->ew_count; i++)
        {
            strcpy(dest->err_word[i], data.strs[i + 4 + dest->kw_count]);
            // printf("err_word[%d]: %s\n", i, dest->err_word[i]);
        }
    }
    strcpy(dest->storage, data.strs[4 + dest->kw_count + dest->ew_count]);
    // printf("storage: %s\n", dest->storage);
}
/*
 * @description        : 获取并解析详细配置文件
 * @param - *manag     : 测试管理结构体
 * @param - *file_name : 配置文件名
 * @return	           : 正常返回0，未找到返回-1
 */
int func_get_conf(struct_test *manag, char *file_name)
{
    int ret = -1;
    config_t cfg;
    config_setting_t *setting;
    // const char *str;
    int case_numbs = 0;
    int i = 0, j = 0, k = 0, l = 0;
    int value = 0;
    char temp_str[50] = {0};
    char temp_title[SER_NUMB_LEN] = {0};
    struct_test_item *item = NULL;
    char temp_form[100] = {0};

    config_init(&cfg);

    /* Read the file. If there is an error, report it and exit. */
    if (!config_read_file(&cfg, file_name))
    {
        fprintf(stderr, "%s:%d - %s\n", config_error_file(&cfg),
                config_error_line(&cfg), config_error_text(&cfg));
        config_destroy(&cfg);
        return ret;
    }

    // my_config_lookup_string(&cfg, "tester_power_path", manag->tester_power_path);
    // my_config_lookup_string(&cfg, "tty_dev", manag->dev);
    my_config_lookup_string(&cfg, "net_dev", manag->net_dev);
    if (config_lookup_int(&cfg, "enter_sys_time", &value))
    {
        manag->sys_start.time = value * 1000;
    }
    else
    {
        manag->sys_start.time = 10 * 1000;
    }
    my_config_lookup_string(&cfg, "emmc_nand_name", manag->sys_start.emmc_dev);
    if (!(my_config_lookup_string(&cfg, "app_path", manag->app_path)))
    {
        strcpy(manag->app_path, "/auto");
    }
    // 关于进入控制台的配置
    my_config_lookup_string(&cfg, "first_str1", manag->sys_start.first_strs[0]);
    my_config_lookup_string(&cfg, "first_str2", manag->sys_start.first_strs[1]);
    my_config_lookup_string(&cfg, "enter_console_step1", manag->console.step[0].cmd);
    if (strstr(manag->console.step[0].cmd, "ETX") != NULL)
    {
        manag->console.step[0].cmd[0] = 0x03;
        manag->console.step[0].cmd[1] = 0x0;
        manag->console.step[0].cmd[2] = 0x0;
    }
    my_config_lookup_string(&cfg, "enter_console_key1", manag->console.step[0].key);
    my_config_lookup_string(&cfg, "enter_console_step2", manag->console.step[1].cmd);
    my_config_lookup_string(&cfg, "enter_console_key2", manag->console.step[1].key);
    my_config_lookup_string(&cfg, "exit_console", manag->console.exit.cmd);
    // 关于进入文件系统的配置
    my_config_lookup_string(&cfg, "enter_key", manag->sys_start.enter_key);
    my_config_lookup_string(&cfg, "user_name", manag->sys_start.user_name);
    my_config_lookup_string(&cfg, "pw_key", manag->sys_start.pw_key);
    my_config_lookup_string(&cfg, "password", manag->sys_start.pass_word);
    my_config_lookup_string(&cfg, "login_str", manag->sys_start.login_str);
    my_config_lookup_string(&cfg, "ftp_uname", manag->sys_start.ftp_uname);
    my_config_lookup_string(&cfg, "ftp_pw", manag->sys_start.ftp_pw);
    my_config_lookup_string(&cfg, "close_echo", manag->sys_start.close_echo);
    for (i = 0; i < MAX_KEEP_CHECK_COUNT; i++)
    {
        sprintf(temp_str, "check_str_B%d", i);
        my_config_lookup_string(&cfg, temp_str, keep_check.keep_check_str[i]);
        if (strlen(keep_check.keep_check_str[i]) > 0)
        {
            keep_check.keep_check_count = i + 1;
        }
    }
    // 获取测试用例数目
    if (config_lookup_int(&cfg, "test_case_number", &case_numbs))
    {
        if (case_numbs > (MAX_ITEM * 4))
            case_numbs = (MAX_ITEM * 4);
    }
    else
    {
        config_destroy(&cfg);
        return ret;
    }
    struct_class_list class_list;
    char class_name[500] = {0};
    int class_name_count = 0;
    if (my_config_lookup_string(&cfg, "class_name", class_name))
        // printf("Store class_name: %s\n\n", class_name);
        ;
    else
        fprintf(stderr, "No 'class_name' setting in param.cfg file.\n");
    class_name_count = func_split_string(class_name, &class_list);

    // 获取测试模式，测试命令等
    for (i = 0; i < case_numbs; i++)
    {
        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "test_case%d_.title", (i + 1));
        my_config_lookup_string(&cfg, temp_str, temp_title);
        item = bsp_title_to_index(manag, temp_title, &class_list, class_name_count);
        if (item == NULL)
        {
            printf("No 'title' setting in conf.cfg file.\n");
            continue;
        }
        strcpy(item->title, temp_title);
        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "test_case%d_.model", (i + 1));
        if (config_lookup_int(&cfg, temp_str, &value))
        {
            item->modle = value;
        }
        else
        {
            item->modle = 3;
        }
        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "test_case%d_.res_formula", (i + 1));
        my_config_lookup_string(&cfg, temp_str, item->res_formula);

        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "test_case%d_.res_formatting", (i + 1));
        my_config_lookup_string(&cfg, temp_str, item->res_formatting);

        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "test_case%d_.data_formatting", (i + 1));
        my_config_lookup_string(&cfg, temp_str, item->data_formatting);

        for (j = 0; j < (MAX_GROUP - 1); j++)
        {
            memset(temp_str, 0, sizeof(temp_str));
            sprintf(temp_str, "test_case%d_.group%d_", (i + 1), (j + 1));
            setting = config_lookup(&cfg, temp_str);
            if (setting != NULL)
            {
                // 有几组
                item->group_count = j + 1;

                int count = config_setting_length(setting);
                if (count > MAX_EXE_LINE)
                    count = MAX_EXE_LINE;
                item->group[j].line_count = count;

                for (k = 0; k < count; ++k)
                {
                    config_setting_t *function = config_setting_get_elem(setting, k);
                    if (config_setting_lookup_int(function, "cmd_type", &value))
                    {
                        item->group[j].line[k].flag = value;
                    }
                    else
                    {
                        item->group[j].line[k].flag = 0;
                    }

                    if (my_config_setting_lookup_string(function, "cmd_content", item->group[j].line[k].content) == 0)
                        continue;
                    /*if (my_config_setting_lookup_string(function, "check_file", item->group[j].line[k].check_file_path) == 0)
                    {
                        if (item->group[j].line[k].flag == 0)
                            continue;
                    }*/
                    if (config_setting_lookup_int(function, "ress", &value))
                    {
                        if (value > VALUE_NUMB)
                            value = VALUE_NUMB;

                        item->group[j].line[k].res_count = value;
                        if (value > 0)
                        {
                            for (l = 0; l < value; l++)
                            {
                                memset(temp_str, 0, sizeof(temp_str));
                                sprintf(temp_str, "res%d_", (l + 1));
                                if (my_config_setting_lookup_string(function, temp_str, temp_form) == 0)
                                    continue;
                                else
                                    bsp_get_result_str_to_form(temp_form, &(item->group[j].line[k].get_res[l]));
                            }
                        }
                    }
                    else
                    {
                        item->group[j].line[k].res_count = 0;
                    }
                    if (config_setting_lookup_int(function, "res_reasons", &value))
                    {
                        if (value > VALUE_NUMB)
                            value = VALUE_NUMB;

                        item->group[j].line[k].res_reason_count = value;
                        if (value > 0)
                        {
                            for (l = 0; l < value; l++)
                            {
                                memset(temp_str, 0, sizeof(temp_str));
                                sprintf(temp_str, "res_reason%d_", (l + 1));
                                if (my_config_setting_lookup_string(function, temp_str, temp_form) == 0)
                                    continue;
                                else
                                    bsp_get_value_str_to_form(temp_form, &(item->group[j].line[k].get_res_reason[l]));
                            }
                        }
                    }
                    else
                    {
                        item->group[j].line[k].res_reason_count = 0;
                    }
                    if (config_setting_lookup_int(function, "datas", &value))
                    {
                        if (value > VALUE_NUMB)
                            value = VALUE_NUMB;

                        item->group[j].line[k].data_count = value;
                        if (value > 0)
                        {
                            for (l = 0; l < value; l++)
                            {
                                memset(temp_str, 0, sizeof(temp_str));
                                sprintf(temp_str, "data%d_", (l + 1));
                                if (my_config_setting_lookup_string(function, temp_str, temp_form) == 0)
                                    continue;
                                else
                                    bsp_get_value_str_to_form(temp_form, &(item->group[j].line[k].get_data[l]));
                            }
                        }
                    }
                    else
                    {
                        item->group[j].line[k].data_count = 0;
                    }
                }
            }
        }
        memset(temp_str, 0, sizeof(temp_str));
        sprintf(temp_str, "test_case%d_.groupR_", (i + 1));
        setting = config_lookup(&cfg, temp_str);
        if (setting != NULL)
        {
            // 有几组
            j = item->group_count;
            item->group_recover = item->group_count;
            item->group_count += 1;

            int count = config_setting_length(setting);
            if (count > MAX_EXE_LINE)
                count = MAX_EXE_LINE;
            item->group[j].line_count = count;

            for (k = 0; k < count; ++k)
            {
                config_setting_t *function = config_setting_get_elem(setting, k);
                if (config_setting_lookup_int(function, "cmd_type", &value))
                {
                    item->group[j].line[k].flag = value;
                }
                else
                {
                    item->group[j].line[k].flag = 0;
                }

                if (my_config_setting_lookup_string(function, "cmd_content", item->group[j].line[k].content) == 0)
                    continue;
                if (config_setting_lookup_int(function, "ress", &value))
                {
                    if (value > VALUE_NUMB)
                        value = VALUE_NUMB;

                    item->group[j].line[k].res_count = value;
                    if (value > 0)
                    {
                        for (l = 0; l < value; l++)
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "res%d_", (l + 1));
                            if (my_config_setting_lookup_string(function, temp_str, temp_form) == 0)
                                continue;
                            else
                                bsp_get_result_str_to_form(temp_form, &(item->group[j].line[k].get_res[l]));
                        }
                    }
                }
                else
                {
                    item->group[j].line[k].res_count = 0;
                }
                if (config_setting_lookup_int(function, "res_reasons", &value))
                {
                    if (value > VALUE_NUMB)
                        value = VALUE_NUMB;

                    item->group[j].line[k].res_reason_count = value;
                    if (value > 0)
                    {
                        for (l = 0; l < value; l++)
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "res_reason%d_", (l + 1));
                            if (my_config_setting_lookup_string(function, temp_str, temp_form) == 0)
                                continue;
                            else
                                bsp_get_value_str_to_form(temp_form, &(item->group[j].line[k].get_res_reason[l]));
                        }
                    }
                }
                else
                {
                    item->group[j].line[k].res_reason_count = 0;
                }
                if (config_setting_lookup_int(function, "datas", &value))
                {
                    if (value > VALUE_NUMB)
                        value = VALUE_NUMB;

                    item->group[j].line[k].data_count = value;
                    if (value > 0)
                    {
                        for (l = 0; l < value; l++)
                        {
                            memset(temp_str, 0, sizeof(temp_str));
                            sprintf(temp_str, "data%d_", (l + 1));
                            if (my_config_setting_lookup_string(function, temp_str, temp_form) == 0)
                                continue;
                            else
                                bsp_get_value_str_to_form(temp_form, &(item->group[j].line[k].get_data[l]));
                        }
                    }
                }
                else
                {
                    item->group[j].line[k].data_count = 0;
                }
            }
        }
    }
    /*
        int m = 0;
        for (i = 0; i < 28; i++)
        {
            for (j = 0; j < MAX_ITEM; j++)
            {
                if (manag->class[i]->function.item[j].group_count != 0)
                {
                    printf("i=%d,j=%d,title = %s\n", i, j, manag->class[i]->function.item[j].title);
                    for (k = 0; k < manag->class[i]->function.item[j].param_count; k++)
                    {
                        printf("type = %d\n", manag->class[i]->function.item[j].param[k].type);
                    }
                    printf("modle=%d\n", manag->class[i]->function.item[j].modle);
                    for (k = 0; k < manag->class[i]->function.item[j].group_count; k++)
                    {
                        for (l = 0; l < manag->class[i]->function.item[j].group[k].line_count; l++)
                        {
                            printf("flag = %d,content = %s\n", manag->class[i]->function.item[j].group[k].line[l].flag, manag->class[i]->function.item[j].group[k].line[l].content);
                            for (m = 0; m < manag->class[i]->function.item[j].group[k].line[l].res_count; m++)
                                printf("key_word = %s,modle = %d,kw_count = %d,ew_count = %d,storage = %s\n", manag->class[i]->function.item[j].group[k].line[l].get_res[m].key_word, manag->class[i]->function.item[j].group[k].line[l].get_res[m].modle, manag->class[i]->function.item[j].group[k].line[l].get_res[m].kw_count, manag->class[i]->function.item[j].group[k].line[l].get_res[m].ew_count, manag->class[i]->function.item[j].group[k].line[l].get_res[m].storage);
                            for (m = 0; m < manag->class[i]->function.item[j].group[k].line[l].data_count; m++)
                                printf("key_word = %s,modle = %d,storage = %s\n", manag->class[i]->function.item[j].group[k].line[l].get_data[m].key_word, manag->class[i]->function.item[j].group[k].line[l].get_data[m].modle, manag->class[i]->function.item[j].group[k].line[l].get_data[m].storage);
                            for (m = 0; m < manag->class[i]->function.item[j].group[k].line[l].res_reason_count; m++)
                                printf("key_word = %s,modle = %d,storage = %s\n", manag->class[i]->function.item[j].group[k].line[l].get_res_reason[m].key_word, manag->class[i]->function.item[j].group[k].line[l].get_res_reason[m].modle, manag->class[i]->function.item[j].group[k].line[l].get_res_reason[m].storage);
                        }
                    }
                }
            }
        }*/
    config_destroy(&cfg);
    return 0;
}

/*
 * @description        : 获取并解析参数配置文件
 * @param - *info      : 信息结构体
 * @return	           : 正常返回0，未找到返回-1
 */
int func_get_platform(struct_info *info)
{
    config_t cfg;
    config_setting_t *setting;
    char platform_name[50] = {0};
    char temp_str[100] = {0};
    int temp_var[3] = {0};

    strcpy(platform_name, info->platform);
    config_init(&cfg);

    /* Read the file. If there is an error, report it and exit. */
    if (!config_read_file(&cfg, platform_file))
    {
        fprintf(stderr, "%s:%d - %s\n", config_error_file(&cfg),
                config_error_line(&cfg), config_error_text(&cfg));
        config_destroy(&cfg);
        return -1;
    }

    sscanf(info->kernel_version, "%d.%d.%d", &temp_var[0], &temp_var[1], &temp_var[2]);
    sprintf(temp_str, "%s.V%d_%d_%d", platform_name, temp_var[0], temp_var[1], temp_var[2]);
    setting = config_lookup(&cfg, temp_str);
    if (setting != NULL)
    {
        config_setting_length(setting);

        config_setting_t *function = config_setting_get_elem(setting, 0);
        if ((my_config_setting_lookup_string(function, "factory", info->factory)) == 0)
        {
            printf("No 'factory' setting in platform.cfg file.\n");
            config_destroy(&cfg);
            return -1;
        }
        if ((my_config_setting_lookup_string(function, "platform", info->platform)) == 0)
        {
            printf("No 'platform' setting in platform.cfg file 1.\n");
            config_destroy(&cfg);
            return -1;
        }
    }
    else
    {
        printf("No 'platform' setting in platform.cfg file 2.\n");
        config_destroy(&cfg);
        return -1;
    }
    printf("info->factory: %s\n", info->factory);
    printf("info->platform: %s\n", info->platform);
    config_destroy(&cfg);
    return 1;
}
