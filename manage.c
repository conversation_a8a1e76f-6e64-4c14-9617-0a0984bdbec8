
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdarg.h>
#include <ctype.h>
#include <sys/epoll.h>
#include <poll.h>
#include <pthread.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <dirent.h>
#include "config_file.h"
#include "manage.h"
#include "file.h"
#include "serial.h"
#include "display.h"
#include "md5.h"
/*
 * @description    : 初始化测试内存，按class分开申请
 * @param - *test  : 测试结构体指针
 * @return		   : 0 成功，-1 失败
 */
int func_init_test_memory(struct_test *test)
{
    int i;

    if (test == NULL)
        return -1;

    // 申请指针数组
    test->test_class = (struct_test_class **)calloc(MAX_CLASS, sizeof(struct_test_class *));
    if (test->test_class == NULL)
    {
        printf("Failed to allocate memory for test_class pointer array\n");
        return -1;
    }

    // 逐个申请每个class的内存
    for (i = 0; i < MAX_CLASS; i++)
    {
        test->test_class[i] = (struct_test_class *)calloc(1, sizeof(struct_test_class));
        if (test->test_class[i] == NULL)
        {
            printf("Failed to allocate memory for test_class[%d]\n", i);
            // 释放已申请的内存
            func_free_test_memory(test);
            return -1;
        }
    }

    printf("Successfully allocated memory for %d test classes\n", MAX_CLASS);
    return 0;
}

/*
 * @description    : 释放测试内存
 * @param - *test  : 测试结构体指针
 * @return		   : 无
 */
void func_free_test_memory(struct_test *test)
{
    int i;

    if (test == NULL || test->test_class == NULL)
        return;

    // 释放每个class的内存
    for (i = 0; i < MAX_CLASS; i++)
    {
        if (test->test_class[i] != NULL)
        {
            free(test->test_class[i]);
            test->test_class[i] = NULL;
        }
    }

    // 释放指针数组
    free(test->test_class);
    test->test_class = NULL;

    printf("Successfully freed test memory\n");
}

/*
 * @description    : 自己的system2
 * @param - command: 要执行的命令
 * @return		   : 0 ：执行成功 -1 ；执行失败
 */
void bsp_my_system2(char *command)
{
    FILE *fp = NULL;

    fp = popen(command, "w");

    if (fp == NULL)
    {
        printf("%s execution error\n", command);
        return;
    }
    else
    {
        pclose(fp);
    }
}
/*
 * @description        : 获取总内存，剩余内存，占用率
 * @param - *mem_total : 总内存大小
 * @param - *mem_free  : 剩余内存大小
 * @param - *mem       ：内存占用率指针
 * @return		       : 无
 */
void func_get_mem_employ(long *mem_total, long *mem_free, float *mem)
{
    long total = 0, free = 0;
    float all = 0.0f, used = 0.0f;
    struct_get_value_form from;
    struct_data_class data;

    // 获取平台信息
    bsp_sync_read_point();
    save_no_time = 1;
    func_send_frame_nr(tty_fd, "cat /proc/meminfo", strlen("cat /proc/meminfo"));
    usleep(500000);
    memset(&data, 0, sizeof(struct_data_class));
    strcpy(from.key_word, "MemTotal:");
    from.modle = ONE_ROW;
    from.x = 0;
    from.y = 0;
    pthread_mutex_lock(&mutex);
    if (bsp_get_value(pLogFile, read_point, &from, &data))
    {
        sscanf(data.data_str, "%*[^0-9]%ld", &total);
    }
    else
    {
        save_no_time = 0;
        pthread_mutex_unlock(&mutex);
        return;
    }
    strcpy(from.key_word, "MemFree:");
    memset(&data, 0, sizeof(struct_data_class));
    if (bsp_get_value(pLogFile, read_point, &from, &data))
    {
        sscanf(data.data_str, "%*[^0-9]%ld", &free);
    }
    else
    {
        save_no_time = 0;
        pthread_mutex_unlock(&mutex);
        return;
    }
    save_no_time = 0;
    pthread_mutex_unlock(&mutex);

    if ((0 != total) && (0 != free))
    {
        *mem_total = total;
        *mem_free = free;
        all = total * 1.0f;
        used = free * 1.0f;
        used = all - used;
        *mem = ((used * 100.0f) / all);
    }
    else
    {
        return;
    }
}
/*
 * @description          : 获取flash总大小
 * @param - *flash_total : flash总内存大小
 * @return		         : 无
 */
void func_get_total_flash(struct_sys_start *sys, unsigned long long *flash_total)
{
    char temp_line[100] = {0};
    struct_get_value_form from;
    struct_data_class data;
    char *temp_str = NULL;
    int len = 0;

    // 获取平台信息
    save_no_time = 1;
    bsp_sync_read_point();

    sprintf(temp_line, "cat /sys/block/%s/size", sys->emmc_dev);
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    usleep(500000);
    from.modle = ALL;
    strcpy(from.key_word, "null");
    memset(&data, 0, sizeof(struct_data_class));
    pthread_mutex_lock(&mutex);
    if (bsp_get_value(pLogFile, read_point, &from, &data))
    {
        sscanf(data.data_str, "%*[^0-9]%llu%*[^0-9]", flash_total);
        if (*flash_total == 0)
        {
            sscanf(data.data_str, "%llu%*[^0-9]", flash_total);
        }
        if (*flash_total == 0)
        {
            char *endptr;
            *flash_total = strtoull(data.data_str, &endptr, 10);
        }
        if (*flash_total == 0)
        {
            temp_str = strstr(data.data_str, temp_line);
            if (temp_str != NULL)
            {
                len = strlen(temp_line);
                temp_str += len;
                sscanf(temp_str, "%*[^0-9]%llu", flash_total);
            }
        }
    }
    if (*flash_total == 0)
    {
        printf("get flash total size failed\n");
    }
    save_no_time = 0;
    pthread_mutex_unlock(&mutex);
}
/*
 * @description    : 将读指针指到文件开始
 * @return		   : 无
 */
void bsp_start_read_point(void)
{
    pthread_mutex_lock(&mutex);
    fseek(pLogFile, 0, SEEK_SET);
    read_point = ftell(pLogFile);
    pthread_mutex_unlock(&mutex);
}
/*
 * @description    : 获取平台及厂家
 * @param - *info  : 平台信息结构体
 * @return		   : 正常返回0，异常返回-1
 */
int func_get_platform_ver(struct_info *info)
{
    struct_get_value_form from;
    char temp[6][50] = {0};
    int check_flag = 0;
    char *temp_str = NULL;
    struct_data_class data;
    char *point = NULL;

    // 获取平台信息
    memset(&from, 0, sizeof(struct_get_value_form));
    bsp_start_read_point();
    strcpy(from.key_word, "Model:");
    from.modle = ONE_ROW;
    from.x = 0;
    from.y = 0;
    memset(&data, 0, sizeof(struct_data_class));
    pthread_mutex_lock(&mutex);
    if (bsp_get_value(pLogFile, read_point, &from, &data) == 1)
    {
        memset(temp, 0, sizeof(temp));
        if ((point = strstr(data.data_str, "[RX]:")) != NULL)
        {
            point += strlen("[RX]:");
            sscanf(point, "%s %s %s", temp[0], temp[1], temp[2]);
        }
        else
            sscanf(data.data_str, "%s %s %s", temp[0], temp[1], temp[2]);
        if ((strstr(temp[1], "forlinx") != NULL) || (strstr(temp[1], "Forlinx") != NULL) || ((strstr(temp[1], "FSL") != NULL)) || ((strstr(temp[1], "Rockchip") != NULL)))
        {
            strcpy(info->platform, temp[2]);
            // 如果字符串里有.就替换为-
            temp_str = strstr(info->platform, ".");
            if (temp_str != NULL)
                *temp_str = '-';
            check_flag = 1;
        }
        else
        {
            strcpy(info->platform, temp[1]);
            check_flag = 1;
        }
        printf("info->platform = %s\n", info->platform);
    }
    else
    {
        strcpy(from.key_word, "Machine model:");
        from.modle = ONE_ROW;
        from.x = 0;
        from.y = 0;
        memset(&data, 0, sizeof(struct_data_class));
        if (bsp_get_value(pLogFile, read_point, &from, &data) == 1)
        {
            memset(temp, 0, sizeof(temp));
            if ((point = strstr(data.data_str, "[RX]:")) != NULL)
            {
                point += strlen("[RX]:");
                sscanf(point, "%s %s %s %s %s", temp[0], temp[1], temp[2], temp[3], temp[4]);
            }
            else
                sscanf(data.data_str, "%s %s %s %s %s", temp[0], temp[1], temp[2], temp[3], temp[4]);
            if (strstr(temp[3], "model:") != NULL)
            {
                strcpy(info->platform, temp[4]);
                check_flag = 1;
            }
        }
        printf("info->platform = %s\n", info->platform);
    }
    pthread_mutex_unlock(&mutex);
    if (check_flag == 1)
    {
        pthread_mutex_lock(&mutex);
        // 获取文件系统版本
        strcpy(from.key_word, "Linux version");
        strcpy(from.key_word2, "version");
        from.modle = KEYWORD2_STR;
        from.x = 0;
        from.y = 0;
        memset(&data, 0, sizeof(struct_data_class));
        if (bsp_get_value(pLogFile, read_point, &from, &data) == 1)
        {
            temp_str = strstr(data.data_str, "-");
            if (temp_str != NULL)
                *temp_str = '\0';
            char data_form[2][50] = {0};
            sscanf(data.data_str, "%s %s", data_form[0], data_form[1]);
            strcpy(info->kernel_version, data_form[0]);
        }
        else
        {
            strcpy(from.key_word, "OS: Linux");
            strcpy(from.key_word2, "OS: Linux, ");
            from.modle = KEYWORD2_STR;
            from.x = 0;
            from.y = 0;
            memset(&data, 0, sizeof(struct_data_class));
            if (bsp_get_value(pLogFile, read_point, &from, &data) == 1)
            {
                strcpy(info->kernel_version, data.data_str);
            }
            else
            {
                printf("get kernel version failed\n");
            }
        }
        printf("info->kernel_version = %s\n", info->kernel_version);

        // 获取uboot版本
        strcpy(from.key_word, "U-Boot SPL");
        memset(from.key_word2, 0, KEY_LEN1);
        from.modle = ONE_ROW;
        from.x = 0;
        from.y = 0;
        strcpy(from.not_word, "init");
        memset(&data, 0, sizeof(struct_data_class));
        if (bsp_get_value(pLogFile, read_point, &from, &data) == 1)
        {
            memset(temp, 0, sizeof(temp));
            if ((point = strstr(data.data_str, "[RX]:")) != NULL)
            {
                point += strlen("[RX]:");
                sscanf(point, "%s %s %s %s %s", temp[0], temp[1], temp[2], temp[3], temp[4]);
            }
            else
                sscanf(data.data_str, "%s %s %s %s %s", temp[0], temp[1], temp[2], temp[3], temp[4]);
            if (strstr(temp[2], "SPL") != NULL)
            {
                strcpy(temp[2], temp[3]);
            }
            temp_str = strstr(temp[2], "-");
            if (temp_str != NULL)
                *temp_str = '\0';
            strcpy(info->uboot_version, temp[2]);
            printf("info->uboot_version=%s\n", info->uboot_version);
        }
        else
        {
            strcpy(from.key_word, "U-Boot");
            from.modle = ONE_ROW;
            from.x = 0;
            from.y = 0;
            strcpy(from.not_word, "init");
            memset(&data, 0, sizeof(struct_data_class));
            if (bsp_get_value(pLogFile, read_point, &from, &data) == 1)
            {
                memset(temp, 0, sizeof(temp));
                if ((point = strstr(data.data_str, "[RX]:")) != NULL)
                {
                    point += strlen("[RX]:");
                    sscanf(point, "%s %s %s %s %s", temp[0], temp[1], temp[2], temp[3], temp[4]);
                }
                else
                    sscanf(data.data_str, "%s %s %s %s %s", temp[0], temp[1], temp[2], temp[3], temp[4]);
                temp_str = strstr(temp[1], "-");
                if (temp_str != NULL)
                    *temp_str = '\0';

                strcpy(info->uboot_version, temp[1]);
                printf("info->uboot_version=%s\n", info->uboot_version);
            }
            else
                strcpy(info->uboot_version, "unknow!");
        }
        pthread_mutex_unlock(&mutex);
        bsp_sync_read_point();
    }
    else
    {
        return -1;
    }
    return 0;
}
/*
 * @description    : 从配置文件矫正平台和厂家
 * @param - *info  : 平台信息结构体
 * @return		   : 正常返回0，异常返回-1
 */
int func_correct_platform_fac(struct_info *info)
{
    int result = 0;
    result = func_get_platform(info);
    return result;
}
/*
 * @description    : 获取平台内存大小 flash大小
 * @param - *info  : 平台信息结构体
 * @return		   : 正常返回0，异常返回-1
 */
int func_get_platform_info(struct_sys_start *sys, struct_info *info)
{
    unsigned long long flash_total = 0;
    long mem_total = 0, mem_free = 0;
    float mem = 0.0, flash = 0.0, mem_total_f = 0.0;

    // 获取平台信息
    func_get_total_flash(sys, &flash_total);
    flash = (flash_total * 1.0f);
    flash = flash / 2.0 / 1024.0 / 1024.0; // flash_total*512/1024/1024;
    flash = (int)(flash * 100 + 0.5) / 100.0;
    info->flash_size = flash;
    func_get_mem_employ(&mem_total, &mem_free, &mem);
    mem_total_f = mem_total * 1.0f;
    mem_total_f = mem_total_f / 1024.0 / 1024.0;
    mem_total_f = (int)(mem_total_f * 100 + 0.5) / 100.0;
    info->mem_size = mem_total_f;
    return 0;
}
/*
 * @description       : 测试管理组织发送数据到网络。
 * @return		      : 无
 */
void func_organaize_send_data(void)
{
    if (send_to_net.flag == 1)
    {
        return;
    }
    if (frame_queue->read_point == frame_queue->write_point)
    {
        return;
    }

    if (NET_SEND_RES == frame_queue->frame[frame_queue->read_point].flag)
    {
        strcpy(send_to_net.data, frame_queue->frame[frame_queue->read_point].data);
        send_to_net.frame_flag = frame_queue->frame[frame_queue->read_point].frame_flag;
        send_to_net.frame_num = frame_queue->frame[frame_queue->read_point].frame_num;
        main_send_event_to_net_inter(NET_SEND_RES);
        send_to_net.flag = 1;
        frame_queue->read_point++;
        frame_queue->read_point = frame_queue->read_point % MAX_SEND_QUEUE_LEN;
        return;
    }
    else if (NET_SEND_DATA == frame_queue->frame[frame_queue->read_point].flag)
    {
        strcpy(send_to_net.data, frame_queue->frame[frame_queue->read_point].data);
        send_to_net.frame_flag = frame_queue->frame[frame_queue->read_point].frame_flag;
        send_to_net.frame_num = frame_queue->frame[frame_queue->read_point].frame_num;
        main_send_event_to_net_inter(NET_SEND_DATA);
        send_to_net.flag = 1;
        frame_queue->read_point++;
        frame_queue->read_point = frame_queue->read_point % MAX_SEND_QUEUE_LEN;
        return;
    }
    else
    {
        frame_queue->read_point++;
        frame_queue->read_point = frame_queue->read_point % MAX_SEND_QUEUE_LEN;
    }

    return;
}
/*
 * @description           : 查找下一个测试item。
 * @param - *region       : region结构体
 * @param - *item_cur     : 当前item
 * @return		          : -1 该region没有后续测试项 0正常
 */
int bsp_get_item_numb(struct_test_region *region, int item_cur)
{
    int i = 0;
    for (i = item_cur; i < region->item_count; i++)
    {
        if (enable == region->item[i].item_en)
        {
            region->item_cur = i;
            return 0;
        }
    }
    return -1;
}
/*
 * @description           : 查找下一个测试item。
 * @param - *region       : region结构体
 * @param - *item_cur     : 当前item
 * @return		          : -1 该region没有后续测试项 0正常
 */
int bsp_get_item_numb1(struct_test_region1 *region, int item_cur)
{
    int i = 0;
    for (i = item_cur; i < region->item_count; i++)
    {
        if (enable == region->item[i].item_en)
        {
            region->item_cur = i;
            return 0;
        }
    }
    return -1;
}

/*
 * @description           : 每个功能性能测试。注：每个class测试完再退出
 * @param - *test_para    : 测试结构
 * @param - *region       : region结构体
 * @return		          : -1 暂停测试 0 正常测试完成
 */
int func_test_regions(struct_test *test_para, struct_test_region *region1, struct_test_region1 *region2)
{
    int triger_event = 0;
    struct pollfd pollfds1[2] = {{net_inter_signal_fd[0], POLLIN, 0}, {test_manage_signal_fd[0], POLLIN, 0}};
    int ne, ret, nevents = 0;
    int fd_temp = 0;
    short revents = 0;
    int count = 0;
    struct_test_item *item = NULL;

    if (region1 != NULL)
    {
        if (-1 == bsp_get_item_numb(region1, region1->item_cur))
            return 0;
        item = &region1->item[region1->item_cur];
        func_write_test_step(1, 0, 0, region1->item_cur);
    }
    else if (region2 != NULL)
    {
        if (-1 == bsp_get_item_numb1(region2, region2->item_cur))
            return 0;
        item = &region2->item[region2->item_cur];
        func_write_test_step(1, 0, 0, region2->item_cur);
    }
    else
    {
        return -1;
    }
    main_send_event_to_net_inter(NET_SEND_HEARTBEAT);
    printf("item->title = %s\n", item->title);
    test_para->test_status = TEST_STEP_RUNNING;
    if (pthread_create(&test_manage_thread, NULL, func_one_item_test_thread, (void *)item))
    {
        printf("Pthread func_one_item_test_thread create error\n");
        main_send_event_to_net_inter(NET_ERR_A7);
        sleep(1);
        return -1;
    }
    // 将线程设置为分离状态
    if (pthread_detach(test_manage_thread) != 0)
    {
        perror("pthread_detach");
        main_send_event_to_net_inter(NET_ERR_A7);
        sleep(1);
        return -1;
    }

    nevents = 2;
    while (1)
    {
        do
        {
            ret = poll(pollfds1, nevents, 1 * 1000);
        } while ((ret < 0) && (errno == EINTR));

        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
            goto __Tool_abnormal_and_quit3;
        }

        if (ret == 0)
        {
            count++;
            count = count % 30;
            if (!count)
                main_send_event_to_net_inter(NET_SEND_HEARTBEAT);
            if (frame_queue->read_point != frame_queue->write_point)
                func_organaize_send_data();
            continue;
        }

        for (ne = 0; ne < nevents; ne++)
        {
            fd_temp = pollfds1[ne].fd;
            revents = pollfds1[ne].revents;

            if (revents & (POLLERR | POLLHUP | POLLNVAL))
                goto __Tool_abnormal_and_quit3;

            if ((revents & POLLIN) == 0)
                continue;

            if (fd_temp == net_inter_signal_fd[0])
            {
                if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                {
                    switch (triger_event)
                    {
                    case NET_BACK_STOP_TEST:
                    {
                        main_send_event_to_test_manage(MANAGE_STOP_TEST); // 发送事件给测试管理线程，通知测试管理线程，暂停当前测试项。
                        test_para->test_status = TEST_STEP_STOP_TEST;
                    }
                    break;
                    case NET_BACK_SUSPENSION_TEST:
                    {
                        main_send_event_to_test_manage(MANAGE_SUSPENSION_TEST);
                        test_para->test_status = TEST_STEP_SUSPENSION_TEST;
                    }
                    break;
                    case NET_BACK_CONTINUE_TEST:
                    {
                        test_para->test_status = TEST_STEP_RUNNING;
                        if (region1 != NULL)
                        {
                            // region1->item_cur++;
                            // if (-1 == bsp_get_item_numb(region1, region1->item_cur))
                            //  return 0;
                            item = &region1->item[region1->item_cur];
                        }
                        else if (region2 != NULL)
                        {
                            // region2->item_cur++;
                            // if (-1 == bsp_get_item_numb1(region2, region2->item_cur))
                            //  return 0;
                            item = &region2->item[region2->item_cur];
                        }

                        if (pthread_create(&test_manage_thread, NULL, func_one_item_test_thread, (void *)item))
                        {
                            printf("Pthread func_one_item_test_thread create error\n");
                            goto __Tool_abnormal_and_quit2;
                        }
                        // 将线程设置为分离状态
                        if (pthread_detach(test_manage_thread) != 0)
                        {
                            perror("pthread_detach");
                            goto __Tool_abnormal_and_quit2;
                        }
                    }
                    break;
                    case NET_BACK_SKIP_CURRENT_TEST:
                    {
                        main_send_event_to_test_manage(MANAGE_SKIP_TEST); // 发送事件给测试管理线程，通知测试管理线程，跳过当前测试项。
                    }
                    break;
                    case NET_THREAD_QUITE:
                    {
                        // 创建网络交互线程
                        if (pthread_create(&net_inter_thread, NULL, net_inter_manage, NULL))
                        {
                            printf("Pthread net_inter_manage create error\n");
                            goto __Tool_abnormal_and_quit3;
                        }
                    }
                    break;
                    default:
                        break;
                    }
                }
            }
            if (fd_temp == test_manage_signal_fd[0])
            {
                if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                {
                    switch (triger_event)
                    {
                    case MANAGE_TEST_COMPLETED:
                    {
                        func_organaize_send_data();
                        if (test_para->test_status == TEST_STEP_STOP_TEST)
                        {
                            // 5秒仍未发送完成退出
                            int temp_count = 0;
                            while (frame_queue->read_point != frame_queue->write_point)
                            {
                                func_organaize_send_data();
                                temp_count++;
                                if (temp_count > 5000)
                                {
                                    printf("send res and data timeout!!!!!!!\n");
                                    break;
                                }
                                else
                                    usleep(1000);
                            }
                            return -1;
                        }
                        else if (test_para->test_status == TEST_STEP_SUSPENSION_TEST)
                            continue;
                        else if (test_para->test_status == TEST_STEP_RUNNING)
                        {
                            if (region1 != NULL)
                            {
                                region1->item_cur++;
                                if (-1 == bsp_get_item_numb(region1, region1->item_cur))
                                {
                                    int temp_count = 0;
                                    while (frame_queue->read_point != frame_queue->write_point)
                                    {
                                        func_organaize_send_data();
                                        temp_count++;
                                        if (temp_count > 5000)
                                        {
                                            printf("send res and data timeout!!!!!!!\n");
                                            break;
                                        }
                                        else
                                            usleep(1000);
                                    }
                                    return 0;
                                }
                                item = &region1->item[region1->item_cur];
                                func_write_test_step(1, 0, 0, region1->item_cur);
                            }
                            else if (region2 != NULL)
                            {
                                region2->item_cur++;
                                if (-1 == bsp_get_item_numb1(region2, region2->item_cur))
                                {
                                    int temp_count = 0;
                                    while (frame_queue->read_point != frame_queue->write_point)
                                    {
                                        func_organaize_send_data();
                                        temp_count++;
                                        if (temp_count > 5000)
                                        {
                                            printf("send res and data timeout!!!!!!!\n");
                                            break;
                                        }
                                        else
                                            usleep(1000);
                                    }
                                    return 0;
                                }
                                item = &region2->item[region2->item_cur];
                                func_write_test_step(1, 0, 0, region2->item_cur);
                            }

                            if (pthread_create(&test_manage_thread, NULL, func_one_item_test_thread, (void *)item))
                            {
                                printf("Pthread func_one_item_test_thread create error\n");
                                goto __Tool_abnormal_and_quit2;
                            }
                            // 将线程设置为分离状态
                            if (pthread_detach(test_manage_thread) != 0)
                            {
                                perror("pthread_detach");
                                goto __Tool_abnormal_and_quit2;
                            }
                        }
                    }
                    break;
                    default:
                        break;
                    }
                }
            }
        }
    }
__Tool_abnormal_and_quit3:
    main_send_event_to_test_manage(MANAGE_QUITE_EVENT);
__Tool_abnormal_and_quit2:
    printf("the tooling is abnormal, please cut off the tooling again and then on\n");
    main_send_event_to_net_inter(NET_ERR_A7);
    sleep(1);
    return -1;
}
/*
 * @description           : 每个功能性能测试。注：每个class测试完再退出
 * @param - *test_para    : 测试结构
 * @param - class         : class号
 * @return		          : -1 暂停测试 0正常
 */
int func_test_class_func_perf(struct_test *test_para, int class)
{
    int regions = 0;
    int result = 0;

    if (disable == test_para->test_class[class]->class_en)
        return 0;
    printf("class = %d\n", class);
    regions = test_para->test_class[class]->region_cur;

    for (; regions < 3; regions++)
    {
        if (FUNC == regions)
        {
            if (enable == test_para->test_class[class]->function.region_en)
            {
                printf("class = %d,FUNC\n", class);
                func_write_test_step(0, class, regions, 0);
                result = func_test_regions(test_para, &test_para->test_class[class]->function, NULL);
                if (-1 == result)
                    return -1;
            }
        }
        else if (EXP_FUNC == regions)
        {
            if (enable == test_para->test_class[class]->exp_function.region_en)
            {
                printf("class = %d,EXP_FUNC\n", class);
                func_write_test_step(0, class, regions, 0);
                result = func_test_regions(test_para, NULL, &test_para->test_class[class]->exp_function);
                if (-1 == result)
                    return -1;
            }
        }
        else if (PERF == regions)
        {
            if (enable == test_para->test_class[class]->performance.region_en)
            {
                printf("class = %d,PERF\n", class);
                func_write_test_step(0, class, regions, 0);
                result = func_test_regions(test_para, NULL, &test_para->test_class[class]->performance);
                if (-1 == result)
                    return -1;
            }
        }
    }
    return 0;
}
/*
 * @description           : 每个稳定性测试。注：每个class测试完再退出
 * @param - *test_para    : 测试结构
 * @param - class         : class号
 * @return		          : -1 暂停测试 0正常
 */
int func_test_class_stab(struct_test *test_para, int class)
{
    int result = 0;

    if (disable == test_para->test_class[class]->class_en)
        return 0;
    if (enable == test_para->test_class[class]->stab.region_en)
    {
        printf("class = %d,STAB\n", class);
        func_write_test_step(0, class, STAB, 0);
        result = func_test_regions(test_para, NULL, &test_para->test_class[class]->stab);
        if (-1 == result)
            return -1;
    }
    return 0;
}

/*
 * @description      : 获取GPIO状态
 * @param - *path    : I/O路径
 * @param - *Sta      : 0、1
 * @return		     : -1 异常 1正常
 */
int func_get_gpio_sta(char *path, int *sta)
{
    char buffer[10] = {0};
    int fd = 0;

    if (0 == access(path, F_OK))
    {
        if (0 > (fd = open(path, O_RDONLY)))
        {
            perror("open error");
            return -1;
        }
        read(fd, buffer, 2);
        *sta = atoi(buffer);
        close(fd);
    }
    else
        return -1;
    return 1;
}
/*
 * @description      : 给被测设备上电、掉电
 * @param - *path    : I/O路径
 * @param - Sta      : 0、1
 * @return		     : 无
 */
void func_set_gpio_sta(char *path, int sta)
{
    char buffer[10] = {0};
    int fd = 0;

    fd = open(path, O_RDWR);
    if (fd > 2)
    {
        sprintf(buffer, "%d", sta); // set 1
        write(fd, buffer, strlen(buffer) + 1);
        close(fd);
    }
}
/*
 * @description      : 查找tty设备
 * @param - *path    : USB路径
 * @return		     : 无
 */
void func_find_tty_files(const char *path, char *tty_name)
{
    struct dirent *entry;
    DIR *dp = opendir(path);

    if (dp == NULL)
    {
        perror("opendir");
        return;
    }

    while ((entry = readdir(dp)))
    {
        if (strstr(entry->d_name, "tty") != NULL)
        {
            if (strlen(entry->d_name) > 3)
            {
                strcpy(tty_name, entry->d_name);
                printf("Found: %s\n", entry->d_name);
            }
            else
            {
                // 进入tty目录
                if (entry->d_type == DT_DIR && strcmp(entry->d_name, "tty") == 0)
                {
                    char tty_path[257];
                    snprintf(tty_path, sizeof(tty_path), "%s/%s", path, entry->d_name);
                    func_find_tty_files(tty_path, tty_name);
                }
            }
        }
    }

    closedir(dp);
}
/*
 * @description    : 获取被测是否有工具包
 * @return		   : 正常返回0，异常返回-1
 */
int func_check_tester_tool(void)
{
    bsp_sync_read_point();

    func_send_frame_nr(tty_fd, "ls auto", strlen("ls auto"));
    usleep(500000);
    if (bsp_check_keyword("No such file or directory", READ_POINT_SYNC))
        return -1;
    return 0;
}
/*
 * @description         : 写入运行步骤
 * @param - mode        : mode=0 重新写入，mode=1 修改item_cur
 * @param - item_class  : 当前运行class
 * @param - item_region : 当前运行region
 * @param - item_item   : 当前运行item
 * @return	            : 正常返回0，异常返回-1
 */
int func_write_test_step(int mode, int item_class, int item_region, int item_item)
{
    FILE *tempLogFile = NULL;
    if (mode == 0)
    {
        tempLogFile = file_open("/auto/test_step.txt", WTWR);
        if (NULL == tempLogFile)
        {
            return -1;
        }
        file_write_fmt(tempLogFile, "class_step=%d\n", item_class);
        file_write_fmt(tempLogFile, "region_step=%d\n", item_region);
        file_write_fmt(tempLogFile, "item_step=%d\n", item_item);
    }
    else if (mode == 1)
    {
        char line[500] = {0};
        char new_content[1024] = {0};
        FILE *readFile = file_open("/auto/test_step.txt", ONLY_R);

        if (NULL == readFile)
        {
            return -1;
        }

        // Read the file and modify the item_step line
        while (fgets(line, sizeof(line), readFile) != NULL)
        {
            if (strstr(line, "item_step=") != NULL)
            {
                sprintf(line, "item_step=%d\n", item_item);
            }
            strcat(new_content, line);
        }
        file_close(readFile);

        // Write the modified content back to the file
        tempLogFile = file_open("/auto/test_step.txt", WTWR);
        if (NULL == tempLogFile)
        {
            return -1;
        }

        fprintf(tempLogFile, "%s", new_content);
    }
    file_close(tempLogFile);
    return 0;
}

/*
 * @description        : 获取上次运行步骤
 * @param - *manag     : 测试管理结构体
 * @return	           : 正常返回0，异常返回-1
 */
int func_get_last_test_step(struct_test *manag)
{
    char line[500] = {0};
    int temp_step = 0, count = 0;
    FILE *tempLogFile = NULL;

    tempLogFile = file_open("/auto/test_step.txt", ONLY_R);
    if (NULL == tempLogFile)
    {
        return -1;
    }
    while (!feof(tempLogFile))
    {
        memset(line, 0, 500);
        if (fgets(line, 500, tempLogFile) != NULL)
        {
            if (NULL != (strstr(line, "class_step=")))
            {
                sscanf(line, "class_step=%d", &temp_step);
                if (temp_step > MAX_CLASS)
                    temp_step = MAX_CLASS;
                manag->class_cur = temp_step;
                count++;
            }
            else if (NULL != (strstr(line, "region_step=")))
            {
                sscanf(line, "region_step=%d", &temp_step);
                if (temp_step > OTHER_REGION)
                    temp_step = OTHER_REGION;
                manag->class[manag->class_cur].region_cur = temp_step;
                count++;
            }
            else if (NULL != (strstr(line, "item_step=")))
            {
                sscanf(line, "item_step=%d", &temp_step);
                if (temp_step > MAX_ITEM)
                    temp_step = MAX_ITEM;
                if (FUNC == manag->class[manag->class_cur].region_cur)
                    manag->class[manag->class_cur].function.item_cur = temp_step;
                else if (EXP_FUNC == manag->class[manag->class_cur].region_cur)
                    manag->class[manag->class_cur].exp_function.item_cur = temp_step;
                else if (PERF == manag->class[manag->class_cur].region_cur)
                    manag->class[manag->class_cur].performance.item_cur = temp_step;
                else if (STAB == manag->class[manag->class_cur].region_cur)
                    manag->class[manag->class_cur].stab.item_cur = temp_step;
                // else if (OTHER_REGION == manag->class[manag->class_cur].region_cur)
                // manag->class[manag->class_cur].other.item_cur = temp_step;
                else
                    return -1;
                count++;
            }
        }
    }
    if (count < 3)
        return -1;
    return 0;
}
/*
 * @description        : 通过FTP从服务器获取被测工具包
 * @param - *path      : 路径
 * @param - *file_name : 文件名
 * @return	           : 正常返回0，异常返回-1
 */
int func_get_f_from_ftp_server(char *path, char *file_name)
{
    char cmd[300] = {0};

    sprintf(cmd, "ftpget -u forFTP -p 123456 %s /auto/%s %s/%s", remote_ip, file_name, path, file_name);
    bsp_my_system2(cmd);
    memset(cmd, 0, sizeof(cmd));
    sprintf(cmd, "/auto/%s", file_name);
    if ((access(cmd, 0)) != -1)
        return 0;
    else
        return -1;
}
/*
 * @description        : 通过FTP将文件推至FTP服务器或被测工装
 * @param - *path      : 路径
 * @param - *file_name : 文件名
 * @param - *username  : 用户名
 * @param - *password  : 密码
 * @return	           : 正常返回0，异常返回-1
 */
int func_put_f_to_ftp_server(char *path, char *file_name, char *username, char *password)
{
    char cmd[300] = {0};

    sprintf(cmd, "ftpput -u %s -p %s %s %s/%s %s/%s", username, password, remote_ip, path, file_name, test->app_path, file_name); // 路径和文件名分开，路径是相对路径，文件名是文件名，不带路径
    bsp_my_system2(cmd);
    return 0;
}

/*
 * @description        : 通过SSH将文件推至SSH服务器或被测工装
 * @param - *cmd       : 命令
 * @param - *password  : 文件名
 * @return	           : 正常返回0，异常返回-1
 */
int func_put_f_to_ssh_server(const char *local_file, const char *remote_user, const char *remote_host, const char *remote_path, const char *password)
{
    char cmd[300];
    FILE *fp;

    // 构建 SCP 命令
    snprintf(cmd, sizeof(cmd), "sshpass -p %s scp -o StrictHostKeyChecking=no %s %s@%s:%s", password, local_file, remote_user, remote_host, remote_path);
    // 打开管道执行 SCP 命令
    printf("cmd = %s\n", cmd);
    fp = popen(cmd, "w");
    if (fp == NULL)
    {
        perror("popen");
        return -1;
    }
    // printf("sshpass ok\n");
    //  关闭管道
    int status = pclose(fp);
    if (status == -1)
    {
        perror("pclose");
        return -1;
    }

    return 0;
}
/*
 * @description        : 通过SSH将文件推至SSH服务器或被测工装
 * @param - *cmd       : 命令
 * @param - *password  : 文件名
 * @return	           : 正常返回0，异常返回-1
 */
int func_put_f_to_lftp_server(const char *local_file, const char *remote_user, const char *remote_host, const char *remote_path, const char *password)
{
    char cmd[300];
    FILE *fp;

    // 构建 SCP 命令
    snprintf(cmd, sizeof(cmd), "lftp-arm64v8 -u %s,%s -e \"put %s; bye \" %s", remote_user, password, local_file, remote_host);
    // 打开管道执行 SCP 命令
    printf("cmd = %s\n", cmd);
    fp = popen(cmd, "w");
    if (fp == NULL)
    {
        perror("popen");
        return -1;
    }
    // printf("sshpass ok\n");
    //  关闭管道
    int status = pclose(fp);
    if (status == -1)
    {
        perror("pclose");
        return -1;
    }

    return 0;
}
/*
 * @description        : 通过SSH拉取文件到工装
 * @param - *cmd       : 命令
 * @param - *password  : 文件名
 * @return	           : 正常返回0，异常返回-1
 */
int func_get_f_from_ssh_server(const char *local_file, const char *remote_user, const char *remote_host, const char *remote_path, const char *password)
{
    char cmd[300];
    FILE *fp;

    // 构建 SCP 命令
    snprintf(cmd, sizeof(cmd), "sshpass -p %s scp -r -o StrictHostKeyChecking=no %s@%s:%s %s", password, remote_user, remote_host, remote_path, local_file);
    // 打开管道执行 SCP 命令
    printf("cmd = %s\n", cmd);
    fp = popen(cmd, "w");
    if (fp == NULL)
    {
        perror("popen");
        return -1;
    }
    // printf("sshpass ok\n");
    //  关闭管道
    int status = pclose(fp);
    if (status == -1)
    {
        perror("pclose");
        return -1;
    }

    return 0;
}
/*
 * @description        : 通过lftp拉取文件到工装
 * @param - *cmd       : 命令
 * @param - *password  : 文件名
 * @return	           : 正常返回0，异常返回-1
 */
int func_get_f_from_lftp_server(const char *local_file, const char *remote_user, const char *remote_host, const char *remote_path, const char *password)
{
    char cmd[300];
    FILE *fp;

    // 构建 SCP 命令
    snprintf(cmd, sizeof(cmd), "lftp-arm64v8 -u %s,%s -e \"get %s; bye \" %s", remote_user, password, local_file, remote_host);
    // 打开管道执行 SCP 命令
    printf("cmd = %s\n", cmd);
    fp = popen(cmd, "w");
    if (fp == NULL)
    {
        perror("popen");
        return -1;
    }
    // printf("sshpass ok\n");
    //  关闭管道
    int status = pclose(fp);
    if (status == -1)
    {
        perror("pclose");
        return -1;
    }

    return 0;
}

/*
 * @description        : 被测通过FTP从测试工装获取被测工具包
 * @param - *dev       : 网络设备
 * @param - *username  : 用户名
 * @param - *password  : 密码
 * @return	           : 正常返回0，异常返回-1
 */
int func_tester_get_tool(char *dev, char *uname, char *passwd)
{
    char temp_line[256] = {0};
    int res = -1;
    char temp_path[100] = {0};
    char *point = NULL;

    // 如果是/ueserdata/auto仅保留/userdata
    if (strcmp(test->app_path, "/auto") == 0)
        strcpy(temp_path, "/");
    else
    {
        strcpy(temp_path, test->app_path);
        point = strstr(temp_path, "auto");
        *point = 0;
    }
    // 删除原来的bin包和auto路径
    sprintf(temp_line, "rm -rf %stester.bin", temp_path);
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    usleep(500000);
    sprintf(temp_line, "rm -rf %s", test->app_path);
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    usleep(500000);
    /*
    // 配置被测网络
    sprintf(temp_line, "ifconfig %s up", dev);
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    usleep(500000);
    sprintf(temp_line, "ifconfig %s %s netmask %s", dev, "192.168.0.101", "255.255.255.0");
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    sleep(6);*/
    // 通过ssh下载bin包到被测设备中，如果失败，通过ftp下载bin包到被测设备中，如果失败，返回-1，工装重新上电，重新下载bin包到被测设备中。
    res = func_put_f_to_ssh_server("/auto/tester.bin", uname, "192.168.0.101", temp_path, passwd);
    if (res == -1)
        return -1;
    bsp_sync_read_point();
    memset(temp_line, 0, sizeof(temp_line));
    sprintf(temp_line, "ls %s", temp_path);
    func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
    usleep(500000);
    if (bsp_check_keyword("tester.bin", READ_POINT_SYNC))
    {
        memset(temp_line, 0, sizeof(temp_line));
        sprintf(temp_line, "chmod 777 %stester.bin", temp_path);
        func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
        usleep(500000);
        memset(temp_line, 0, sizeof(temp_line));
        sprintf(temp_line, "%stester.bin", temp_path);
        func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
        usleep(500000);
        func_send_frame_nr(tty_fd, "sync && echo 3 > /proc/sys/vm/drop_caches", strlen("sync && echo 3 > /proc/sys/vm/drop_caches"));
        sleep(2);
        return 0;
    }
    else
    {
        res = func_put_f_to_lftp_server("/auto/tester.bin", uname, "192.168.0.101", temp_path, passwd);
        if (res == -1)
            return -1;
        bsp_sync_read_point();
        memset(temp_line, 0, sizeof(temp_line));
        sprintf(temp_line, "ls %s", temp_path);
        func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
        usleep(500000);
        if (bsp_check_keyword("tester.bin", READ_POINT_SYNC))
        {
            memset(temp_line, 0, sizeof(temp_line));
            sprintf(temp_line, "chmod 777 %stester.bin", temp_path);
            func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
            usleep(500000);
            memset(temp_line, 0, sizeof(temp_line));
            sprintf(temp_line, "%stester.bin", temp_path);
            func_send_frame_nr(tty_fd, temp_line, strlen(temp_line));
            usleep(500000);
            func_send_frame_nr(tty_fd, "sync && echo 3 > /proc/sys/vm/drop_caches", strlen("sync && echo 3 > /proc/sys/vm/drop_caches"));
            sleep(2);
            return 0;
        }
        else
            return -1;
    }
}
/*
 * @description        : 删除测试记录
 * @return	           : 无
 */
void func_delete_test_record(void)
{
    unlink("/auto/");
    unlink("/auto/info.txt");
    unlink(param_file);
    unlink(conf_file);
}
/*
 * @description        : 从FTP获取配置文件，校验MD5，改名字
 * @return	           : 无
 */
int func_ftp_get_param(void)
{
    // char temp_file[7][30] = {0};
    char temp_path[100] = {0};
    char temp_file_name[30] = {0};
    char line[100] = {0};
    char temp_md5[32] = {0};
    // int len = 0;
    char *p_file = NULL;
    FILE *fp = NULL;

    // len = sscanf(param_file, "%s/%s/%s/%s/%s/%s/%s", temp_file[0], temp_file[1], temp_file[2], temp_file[3], temp_file[4], temp_file[5], temp_file[6]);
    // if (len < 1)
    // {
    // printf("param_file err\n");
    // return -1;
    //}
    // strcpy(temp_file_name, temp_file[(len - 1)]);
    p_file = strrchr(param_file, '/');
    *(p_file) = 0;
    strcpy(temp_path, param_file);
    strcat(temp_path, "/");
    strcpy(temp_file_name, (p_file + 1));

    // 从FTP获取配置文件
    if (-1 == func_get_f_from_ftp_server(temp_path, temp_file_name))
    {
        printf("get param.txt err\n");
        return -1;
    }
    // 校验MD5
    sprintf(param_file, "/auto/%s", temp_file_name);
    memset(temp_path, 0, sizeof(temp_path));
    sprintf(temp_path, "md5sum %s", param_file);
    fp = popen(temp_path, "r");
    if (fp != NULL)
    {
        while ((fgets(line, sizeof(line) - 1, fp)) != NULL)
        {
            printf("src md5 = %s\n", line);
            if ((strstr(line, "auto") != NULL))
            {
                sscanf(line, "%[^ ]", temp_md5);
            }
        }
        pclose(fp);
    }
    if (strncmp(temp_md5, param_file_md5, 32) != 0)
    {
        printf("temp_md5 = %s,param_file_md5 = %s\n", temp_md5, param_file_md5);
        printf("md5 err\n");
        return -1;
    }
    // 改名字
    rename(param_file, "/auto/param.cfg");
    memset(param_file, 0, 100);
    strcpy(param_file, "/auto/param.cfg");
    return 0;
}
/*
 * @description        : 获取并解析详细配置文件
 * @param - *manag     : 测试管理结构体
 * @return	           : 正常返回0，异常返回-1，完成返回1
 */
int func_continue_test_check(struct_test *manag, struct_info *get_info)
{
    int triger_event = 0;
    int ret, nevents = 0;
    char temp_line[255] = {0};
    int continue_flag = 0;
    FILE *f;
    char *p1 = NULL, *current = NULL;
    float temp_float = 0;
    char *newline = NULL;

    if ((access(info_file, 0)) != -1)
    {
        f = file_open(info_file, ONLY_R);
        if (NULL != f)
        {
            // 循环读取文件
            while (fgets(temp_line, sizeof(temp_line) - 1, f) != NULL)
            {
                p1 = strstr(temp_line, "plat:");
                if (p1 != NULL)
                {
                    current = p1 + strlen("plat:");
                    newline = strchr(current, '\n');
                    if (newline != NULL)
                    {
                        *newline = '\0';
                    }
                    if (strncmp(current, get_info->platform, strlen(get_info->platform)) != 0)
                        break;
                }
                p1 = strstr(temp_line, "fact:");
                if (p1 != NULL)
                {
                    current = p1 + strlen("fact:");
                    newline = strchr(current, '\n');
                    if (newline != NULL)
                    {
                        *newline = '\0';
                    }
                    if (strncmp(current, get_info->factory, strlen(get_info->factory)) != 0)
                        break;
                }
                p1 = strstr(temp_line, "k_ver:");
                if (p1 != NULL)
                {
                    current = p1 + strlen("k_ver:");
                    newline = strchr(current, '\n');
                    if (newline != NULL)
                    {
                        *newline = '\0';
                    }
                    if (strncmp(current, get_info->kernel_version, strlen(get_info->kernel_version)) != 0)
                        break;
                }
                p1 = strstr(temp_line, "u_ver:");
                if (p1 != NULL)
                {
                    current = p1 + strlen("u_ver:");
                    newline = strchr(current, '\n');
                    if (newline != NULL)
                    {
                        *newline = '\0';
                    }
                    if (strncmp(current, get_info->uboot_version, strlen(get_info->uboot_version)) != 0)
                        break;
                }
                p1 = strstr(temp_line, "mem_size:");
                if (p1 != NULL)
                {
                    current = p1 + strlen("mem_size");
                    temp_float = atof(current);
                    if (abs(temp_float - get_info->mem_size) >= 1.0)
                        break;
                }
                p1 = strstr(temp_line, "flash_size:");
                if (p1 != NULL)
                {
                    current = p1 + strlen("flash_size");
                    temp_float = atof(current);
                    if (abs(temp_float - get_info->mem_size) < 1.0)
                    {
                        continue_flag = 1;
                    }
                }
                memset(temp_line, 0, sizeof(temp_line));
            }
            fclose(f);
        }
        else
        {
            printf("continue test device have not info file\n");
            bsp_lcd_display("发送被测设备已更换，是否继续测试");
            main_send_event_to_net_inter(NET_SEND_TESTER_CHG_CNTE_OR_NOT);
        }
    }
    else
    {
        printf("continue test device have not info file\n");
        bsp_lcd_display("发送被测设备已更换，是否继续测试");
        main_send_event_to_net_inter(NET_SEND_TESTER_CHG_CNTE_OR_NOT);
    }
    // 确认被测设备是否有工具包
    if (1 == continue_flag)
    {
        if (-1 == func_check_tester_tool())
        {
            printf("The tester have not tool\n");
            bsp_lcd_display("发送被测设备已更换，是否继续测试");
            main_send_event_to_net_inter(NET_SEND_TESTER_CHG_CNTE_OR_NOT);
            continue_flag = 0;
        }
        else
        {
            // 获取上次测试到哪里
            if (-1 == func_get_last_test_step(manag))
            {
                printf("The tester have not test step record\n");
                bsp_lcd_display("发送被测设备已更换，是否继续测试");
                main_send_event_to_net_inter(NET_SEND_TESTER_CHG_CNTE_OR_NOT);
                continue_flag = 0;
            }
        }
    }
    else
    {
        printf("continue test device have not info file\n");
        bsp_lcd_display("发送被测设备已更换，是否继续测试");
        main_send_event_to_net_inter(NET_SEND_TESTER_CHG_CNTE_OR_NOT);
    }
    if (1 == continue_flag)
    {
        main_send_event_to_net_inter(NET_SEND_REQUST_COUNTINUE_TEST);
    }
    struct pollfd pollfds[2] = {{net_inter_signal_fd[0], POLLIN, 0}};
    nevents = 1;
    int ne = 0;
    int fd_temp = 0;
    short revents = 0;
    int count = 0;

    while (1)
    {
        do
        {
            ret = poll(pollfds, nevents, 3 * 1000);
        } while ((ret < 0) && (errno == EINTR));

        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
            goto __Tool_abnormal_and_quit;
        }

        if (ret == 0)
        {
            count++;
            count = count % 10;
            if (!count)
                main_send_event_to_net_inter(NET_SEND_HEARTBEAT);
            if (0 == continue_flag)
                main_send_event_to_net_inter(NET_SEND_TESTER_CHG_CNTE_OR_NOT);
            continue;
        }

        for (ne = 0; ne < nevents; ne++)
        {
            fd_temp = pollfds[ne].fd;
            revents = pollfds[ne].revents;

            if (revents & (POLLERR | POLLHUP | POLLNVAL))
            {
                printf("poll error revents = %d\n", revents);
                goto __Tool_abnormal_and_quit;
            }

            if ((revents & POLLIN) == 0)
                continue;
            if (fd_temp == net_inter_signal_fd[0])
            {
                if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                {
                    if (NET_BACK_RESTART_TEST == triger_event)
                    {
                        // 删除记录
                        func_delete_test_record();
                        // 重启设备
                        system("reboot");
                        return -1;
                    }
                    else if (NET_BACK_CONTINUE_TEST == triger_event)
                    {
                        if (0 == continue_flag)
                        {
                            printf("change original tester equipment, and tool powered on again\n");
                            bsp_lcd_display("更换原被测设备后,工装重新上电");
                            return -1;
                        }
                        else
                        {
                            if (-1 == func_get_param(manag, param_file))
                            {
                                printf("The tester Detailed parameter error\n");
                                bsp_lcd_display("详细参数错误");
                                main_send_event_to_net_inter(NET_ERR_A8);
                                sleep(1);
                                // 删除记录
                                func_delete_test_record();
                                // 重启设备
                                system("reboot");

                                return -1;
                            }

                            return 0;
                        }
                    }
                    else
                        continue;
                }
                continue;
            }
        }
    __Tool_abnormal_and_quit:
        printf("the tooling is abnormal, please cut off the tooling again and then on\n");
        bsp_lcd_display("工装异常退出，请给工装重新断电再上电");
        main_send_event_to_net_inter(NET_SEND_TESTER_CHG_CNTE_OR_NOT);
        return -1;
    }
}
/*
 * @description        : 测试前准备流程
 * @param - *manag     : 测试管理结构体
 * @param - *get_info  : 被测信息结构体
 * @return	           : 正常返回0，异常返回-1
 */
int func_test_prepare(struct_test *manag, struct_info *get_info, int flag)
{
    int triger_event = 0;
    int retry = 0;
    FILE *pInfoFile = NULL;
    int ret, nevents = 0;
    char temp_line[255] = {0};
    int result = 0;
    char temp_dev_name[15] = {0};
    int sta_flag = 0;

    manag->sys_start.need_init_tester = 1; // 置为登录系统后关闭回显，设置IP等标志
    // 查找串口名字
    func_find_tty_files("/sys/devices/platform/32f10108.usb/38200000.dwc3/xhci-hcd.0.auto/usb1/1-1/1-1.3/1-1.3:1.0", temp_dev_name);
    sprintf(manag->dev, "/dev/%s", temp_dev_name);
    // 打开交互串口
    tty_fd = open(manag->dev, O_RDWR | O_NOCTTY | O_NDELAY);
    if (tty_fd < 0)
    {
        perror(manag->dev);
        printf("Can't Open Serial Port %s \n", manag->dev);
        main_send_event_to_net_inter(NET_ERR_A1);
        bsp_lcd_display("无法打开串口");
        return -1;
    }
    else
    {
        if ((result = func_set_opt(tty_fd, 115200, 8, 1, 'N', 0)) < 0)
        {
            perror("set_opt error");
            main_send_event_to_net_inter(NET_ERR_A1);
            bsp_lcd_display("无法打开串口");
            return -1;
        }
    }
    if (pthread_create(&log_save_thread, NULL, tty_receive_manage, NULL))
    {
        printf("Pthread tty_receive_manage create error\n");
        return -1;
    }
    sleep(1);

    // 给被测上电 判断GPIO状态确定5V或12V
    func_get_gpio_sta("/sys/class/gpio-input/gpio_input_0/state", &sta_flag);
    if (sta_flag == 1)
        strcpy(manag->tester_power_path, "/sys/class/leds/Test_12v_out/brightness");
    else
        strcpy(manag->tester_power_path, "/sys/class/leds/Test_12v_out/brightness");

    retry = 10;
__repower:
    func_set_gpio_sta(manag->tester_power_path, 1);
    printf("The tester is powered on\n");
    sleep(15);
    // 从起机信息获取平台和厂家
    if (-1 == func_get_platform_ver(get_info))
    {
        if (retry > 0)
        {
            func_set_gpio_sta(manag->tester_power_path, 0);
            sleep(2);
            retry--;
            goto __repower;
        }
        printf("Get tester platform and verson error\n");
        bsp_lcd_display("找不到测试平台和内核版本");
        main_send_event_to_net_inter(NET_ERR_A4);
        return -1;
    }
    // 从配置文件矫正平台和厂家
    if (-1 == func_correct_platform_fac(get_info))
    {
        printf("Correct tester platform and factory error\n");
        bsp_lcd_display("找不到测试平台和工厂");
        main_send_event_to_net_inter(NET_ERR_A4);
        return -1;
    }

    // 下载详细配置文件
    memset(temp_line, 0, sizeof(temp_line));
    sprintf(temp_line, "tester/%s_fixed/%s/%s/", get_info->factory, get_info->platform, get_info->kernel_version);
    if (-1 == func_get_f_from_ftp_server(temp_line, "detail_proc.cfg"))
    {
        printf("FTP download detail_proc.cfg error\n");
        bsp_lcd_display("无法下载被测详细配置文件");
        main_send_event_to_net_inter(NET_ERR_A5);
        return -1;
    }
    // 解析详细配置文件
    if (-1 == func_get_conf(manag, conf_file))
    {
        printf("The tester Detailed parameter error\n");
        bsp_lcd_display("详细参数错误");
        main_send_event_to_net_inter(NET_ERR_A10);
        return -1;
    }

    // 确认被测设备可以进入系统
    retry = 0;
    while (retry < 10)
    {
        if (-1 == func_makesure_sys_start(&(manag->sys_start), manag->sys_start.time))
        {
            func_set_gpio_sta(manag->tester_power_path, 0);
            sleep(2);
            func_set_gpio_sta(manag->tester_power_path, 1);
            sleep(15);
        }
        else
            break;
        retry++;
    }
    if (retry >= 10)
    {
        printf("System status is uncertain\n");
        bsp_lcd_display("被测设备异常,请检查");
        main_send_event_to_net_inter(NET_ERR_A2);
        return -1;
    }
    if (-1 == func_login_sys(&(manag->sys_start)))
    {
        printf("System status is uncertain\n");
        bsp_lcd_display("请检查用户名，密码");
        main_send_event_to_net_inter(NET_ERR_A3);
        return -1;
    }
    printf("Login system success\n");
    sleep(5); // 等待网口配置成功并可用
    // 获取被测设备信息
    if (-1 == func_get_platform_info(&manag->sys_start, get_info))
    {
        printf("Get tester info error\n");
        bsp_lcd_display("找不到测试平台信息");
        main_send_event_to_net_inter(NET_ERR_A4);
        return -1;
    }
    printf("Get tester info success\n");
    // 如果是继续测试，需要与上次测试的平台信息进行比对
    if (1 == flag)
    {
        if (-1 == func_continue_test_check(manag, get_info))
        {
            printf("Continue test check error\n");
            return -1;
        }
        else
        {
            return 0;
        }
    }
    // 将平台信息写入文件
    pInfoFile = file_open(info_file, WTWR);
    if (NULL == pInfoFile)
    {
        printf("Open InfoFile file error\n");
        main_send_event_to_net_inter(NET_ERR_A7);
        return -1;
    }
    else
    {
        fprintf(pInfoFile, "plat:%s\n", get_info->platform);
        fprintf(pInfoFile, "fact:%s\n", get_info->factory);
        fprintf(pInfoFile, "k_ver:%s\n", get_info->kernel_version);
        fprintf(pInfoFile, "u_ver:%s\n", get_info->uboot_version);
        fprintf(pInfoFile, "mem_size:%.2f\n", get_info->mem_size);
        fprintf(pInfoFile, "flash_size:%.2f\n", get_info->flash_size);
        fflush(pInfoFile);
        fclose(pInfoFile);
        // main_send_event_to_net_inter(NET_SEN_TESTER_INFO);
    }

    // 工装从服务器FTP下载工具包
    printf("Download tool from FTP server\n");
    memset(temp_line, 0, sizeof(temp_line));
    sprintf(temp_line, "tester/%s_fixed/%s/%s/", get_info->factory, get_info->platform, get_info->kernel_version);
    if (-1 == func_get_f_from_ftp_server(temp_line, "tester.bin"))
    {
        printf("FTP download tool error\n");
        bsp_lcd_display("无法下载被测工具包");
        main_send_event_to_net_inter(NET_ERR_A5);
        return -1;
    }
    printf("Download tool from FTP server success\n");

    // 给工装发命令，让被测通过FTP下载被测工具包
    if (-1 == func_tester_get_tool(manag->net_dev, manag->sys_start.ftp_uname, manag->sys_start.ftp_pw))
    {
        printf("tester FTP download tool error\n");
        bsp_lcd_display("被测设备无法下载被测工具包");
        main_send_event_to_net_inter(NET_ERR_A6);
        return -1;
    }
    main_send_event_to_net_inter(NET_SEN_TESTER_INFO);
    // 等待配置文件
    bsp_lcd_display("等待下载配置文件");
    struct pollfd pollfds1[2] = {{net_inter_signal_fd[0], POLLIN, 0}};
    int start_flag = 0;
    nevents = 1;
    int ne = 0;
    int fd_temp = 0;
    short revents = 0;
    int count = 0;

    while (1)
    {
        do
        {
            ret = poll(pollfds1, nevents, 3 * 1000);
        } while ((ret < 0) && (errno == EINTR));

        if (ret < 0)
        {
            printf("%s poll=%d, errno: %d (%s)", __func__, ret, errno, strerror(errno));
            goto __Tool_abnormal_and_quit1;
        }

        if (ret == 0)
        {
            count++;
            count = count % 10;
            if (!count)
            {
                main_send_event_to_net_inter(NET_SEND_HEARTBEAT);
            }
            continue;
        }

        for (ne = 0; ne < nevents; ne++)
        {
            fd_temp = pollfds1[ne].fd;
            revents = pollfds1[ne].revents;

            if (revents & (POLLERR | POLLHUP | POLLNVAL))
            {
                printf("poll error revents = %d\n", revents);
                goto __Tool_abnormal_and_quit1;
            }

            if ((revents & POLLIN) == 0)
                continue;
            if (fd_temp == net_inter_signal_fd[0])
            {
                if (read(fd_temp, &triger_event, sizeof(triger_event)) == sizeof(triger_event))
                {
                    if (NET_BACK_PARAM_PATH == triger_event)
                    {
                        // 从指定路径下载参数文件，包括MD5验证，更改名字
                        if (-1 == func_ftp_get_param())
                        {
                            printf("FTP download param error\n");
                            bsp_lcd_display("无法下载被测参数文件");
                            main_send_event_to_net_inter(NET_SEND_PARAM_ERR);
                            continue;
                        }
                        // 解析并验证参数文件
                        if (-1 == func_get_param(manag, param_file))
                        {
                            printf("Get param error\n");
                            bsp_lcd_display("无法解析被测参数文件");
                            main_send_event_to_net_inter(NET_SEND_PARAM_ERR);
                            continue;
                        }
                        else
                        {
                            // 控制呼吸灯
                            main_send_event_to_net_inter(NET_SEND_PARAM_OK);
                            start_flag = 1;
                            bsp_lcd_display("等待开始测试");
                            continue;
                        }
                    }
                    else if (NET_BACK_START_TEST == triger_event)
                    {
                        if (1 == start_flag)
                            return 0;
                        else
                            continue;
                    }
                    else if (NET_THREAD_QUITE == triger_event)
                    {
                        // 创建网络交互线程
                        if (pthread_create(&net_inter_thread, NULL, net_inter_manage, NULL))
                        {
                            printf("Pthread net_inter_manage create error\n");
                            goto __Tool_abnormal_and_quit1;
                        }
                    }

                    else
                        continue;
                }
                continue;
            }
        }
    }
__Tool_abnormal_and_quit1:
    printf("the tooling is abnormal, please cut off the tooling again and then on\n");
    bsp_lcd_display("工装异常退出，请给工装重新断电再上电");
    main_send_event_to_net_inter(NET_ERR_A7);

    return -1;
}
