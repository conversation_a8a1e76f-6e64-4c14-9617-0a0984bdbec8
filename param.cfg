##### auto test parameter list ###############################
#
# This file describes auto test function enable and disable, parameter setting.
#
# Empty lines and lines starting with # are ignored
# Note: Please do not modify. 

#type model : 0-int 1-float 2-str

class_en = "XPU_en,BOOT_en,EMMC_en,NAND_en,SD_en,SATA_en,DDR_en,SPI_en,I2C_en,CAN_en,UART_en,USB_en,PCIE_en,NET_en,WIFI_en,G4G5G_en,BLUETOOTH_en,GPIO_en,PWM_en,WATCHDOG_en,CAMERA_en,AUDIO_en,RTC_en,FS_en,OPENXX_en,RTS_en,ADC_en,OTHER_en";
class_name = "xpus,bootloader,emmc,nand,sdcard,sata,ddr,spi,i2c,can,uart,usb,pcie,net,wifi,g4g5g,bluetooth,gpio,pwm,watchdog,camera,audio,rtc,filesystem,openxx,rts,adc,other";

XPU_en = 1;
xpus =
{
	func_en = 1;
	function = ( { title  = "XPU-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "XPU-F-2";on_off = 1;times = 1;dura = 120;timeout = 150; para_numb = 0;},
				{ title  = "XPU-F-3";on_off = 1;times = 1;dura = 120;timeout = 150; para_numb = 0;},
				{ title  = "XPU-F-4";on_off = 1;times = 1;dura = 120;timeout = 150; para_numb = 0;},
				{ title  = "XPU-F-5";on_off = 1;times = 1;dura = 120;timeout = 150; para_numb = 0;},
				{ title  = "XPU-F-6";on_off = 1;times = 1;dura = 120;timeout = 150; para_numb = 0;},
				{ title  = "XPU-F-8";on_off = 1;times = 1;dura = 120;timeout = 150; para_numb = 0;},
				{ title  = "XPU-F-9";on_off = 1;times = 1;dura = 120;timeout = 150; para_numb = 0;},
				{ title  = "XPU-F-12";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "XPU-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "XPU-PF-2";on_off = 1;times = 1;dura = 1;timeout = 150; para_numb = 0;},
				{ title  = "XPU-PF-3";on_off = 1;times = 1;dura = 1;timeout = 150; para_numb = 0;}
          );
		  
	stab_en = 1;
	stability = ( { title  = "XPU-S-1";on_off = 1;times = 1;dura = 3600;timeout = 4000; para_numb = 0;}
          );
};

BOOT_en = 1;
bootloader =
{
	func_en = 1;
	function = ( { title  = "BOOT-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "BOOT-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "BOOT-F-7";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "BOOT-F-8";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "BOOT-F-12";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 0;
};

EMMC_en = 1;
emmc =
{
	func_en = 1;
	function = ( { title  = "EMMC-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 2;param1 = "HS200";},
				{ title  = "EMMC-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 0;param1 = 8;},
				{ title  = "EMMC-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;type1 = 0;param1 = 8000;},
				{ title  = "EMMC-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "EMMC-F-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "EMMC-F-6";on_off = 1;times = 1;dura = 1;timeout = 150; para_numb = 0;},
				{ title  = "EMMC-F-9";on_off = 1;times = 1;dura = 120;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "EMMC-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "EMMC-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "EMMC-PF-3";on_off = 1;times = 1;dura = 1;timeout = 300; para_numb = 0;}
          );
		  
	stab_en = 0;
};

NAND_en = 1;
nand =
{
	func_en = 1;
	function = ( { title  = "NAND-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "NAND-F-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 0;param1 = 50;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 0;
};

SD_en = 1;
sdcard =
{
	func_en = 1;
	function = ( { title  = "SD/TF-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "SD/TF-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "SD/TF-F-6";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 2;param1 = "/run/media";}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "SD/TF-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "SD/TF-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	stab_en = 1;
	stability = ( { title  = "SD/TF-S-2";on_off = 1;times = 50;dura = 300;timeout = 400; para_numb = 0;}
          );
};

SATA_en = 1;
sata =
{
	func_en = 1;
	function = ( { title  = "SATA-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "SATA-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 2;param1 = "/run/media";},
				{ title  = "SATA-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 2;param1 = "3G";}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "SATA-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "SATA-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	stab_en = 1;
	stability = ( { title  = "SATA-S-1";on_off = 1;times = 50;dura = 1;timeout = 400; para_numb = 0;},
				{ title  = "SATA-S-2";on_off = 1;times = 50;dura = 1;timeout = 400; para_numb = 1;type1 = 2;param1 = "3G";},
				{ title  = "SATA-S-3";on_off = 1;times = 50;dura = 1;timeout = 400; para_numb = 0;},
				{ title  = "SATA-S-4";on_off = 1;times = 50;dura = 1;timeout = 400; para_numb = 1;type1 = 2;param1 = "3G";},
				{ title  = "SATA-S-5";on_off = 1;times = 1;dura = 86400;timeout = 90000; para_numb = 0;},
				{ title  = "SATA-S-6";on_off = 1;times = 10;dura = 1;timeout = 300; para_numb = 0;}
          );
};

DDR_en = 1;
ddr =
{
	func_en = 1;
	function = ( { title  = "DDR-F-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 0;param1 = 1000000;},
				{ title  = "DDR-F-7";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "DDR-F-8";on_off = 1;times = 1;dura = 1;timeout = 500; para_numb = 0;},
				{ title  = "DDR-F-10";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "DDR-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "DDR-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	stab_en = 0;
};

SPI_en = 1;
spi =
{
	func_en = 1;
	function = ( { title  = "SPI-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "SPI-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "SPI-F-5";on_off = 1;times = 1;dura = 1;timeout = 300; para_numb = 0;},
				{ title  = "SPI-F-9";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "SPI-F-10";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "SPI-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	stab_en = 0;
};

I2C_en = 1;
i2c =
{
	func_en = 1;
	function = ( { title  = "I2C-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "I2C-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "I2C-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	stab_en = 0;
};

CAN_en = 1;
can =
{
	func_en = 1;
	function = ( { title  = "CAN-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-6";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-7";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-8";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-9";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-10";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-11";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-12";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-13";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},	
				{ title  = "CAN-F-14";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-15";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAN-F-19";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-6";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-7";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-8";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-9";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-10";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-11";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-12";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-13";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},	
				{ title  = "CANFD-F-14";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CANFD-F-18";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "CAN-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "CAN-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "CAN-PF-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "CAN-PF-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "CANFD-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "CANFD-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "CANFD-PF-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "CANFD-PF-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	stab_en = 0;
};

UART_en = 1;
uart =
{
	func_en = 1;
	function = ( { title  = "UART-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "UART-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "UART-F-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
			);
			
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "UART-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "UART-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "UART-PF-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "UART-PF-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
			);
			
	stab_en = 0;
};

USB_en = 1;
usb =
{
	func_en = 1;
	function = ( { title  = "USB-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "USB-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "USB-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "USB-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "USB-F-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 2;param1 = "/run/media";},
				{ title  = "USB-F-8";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 1;
	exp_function = ({ title  = "USB-EF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 0;param1 = 90;},
				{ title  = "USB-EF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 0;param1 = 200;},
				{ title  = "USB-EF-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );

	perf_en = 1;
	performance = ( { title  = "USB-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "USB-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "USB-PF-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
			);
			
	stab_en = 1;
	stability = ( { title  = "USB-S-1";on_off = 1;times = 50;dura = 1;timeout = 400; para_numb = 0;},
				{ title  = "USB-S-2";on_off = 1;times = 50;dura = 1;timeout = 400; para_numb = 0;}
          );
};

PCIE_en = 1;
pcie =
{
	func_en = 1;
	function = ( { title  = "PCIE-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "PCIE-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "PCIE-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "PCIE-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 0;param1 = 4;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 0;
};

NET_en = 1;
net =
{
	func_en = 1;
	function = ( { title  = "NET-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "NET-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "NET-F-6";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "NET-F-7";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "NET-F-13";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 1;type1 = 0;param1 = 90;}
          );
		  
	exp_func_en = 1;
	exp_function = ({ title  = "NET-EF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );

	perf_en = 1;
	performance = ( { title  = "NET-PF-1";on_off = 1;times = 1;dura = 600;timeout = 1000; para_numb = 1;type1 = 0;param1 = 10;},
					{ title  = "NET-PF-2";on_off = 1;times = 1;dura = 180;timeout = 300; para_numb = 1;type1 = 0;param1 = 5;}
			);
			
	stab_en = 1;
	stability = ( { title  = "NET-S-2";on_off = 1;times = 50;dura = 1;timeout = 400; para_numb = 0;},
				{ title  = "NET-S-3";on_off = 1;times = 50;dura = 1;timeout = 400; para_numb = 0;},
				{ title  = "NET-S-4";on_off = 1;times = 1;dura = 86400;timeout = 90000; para_numb = 0;},
				{ title  = "NET-S-5";on_off = 1;times = 50;dura = 1;timeout = 400; para_numb = 0;},
				{ title  = "NET-S-6";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
};

WIFI_en = 1;
wifi =
{
	func_en = 1;
	function = ( { title  = "WIFI-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "WIFI-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "WIFI-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "WIFI-PF-1";on_off = 1;times = 1;dura = 600;timeout = 1000; para_numb = 1;type1 = 0;param1 = 20;}
			);
			
	stab_en = 1;
	stability = ( { title  = "WIFI-S-1";on_off = 1;times = 1;dura = 86400;timeout = 90000; para_numb = 0;},
          );
};

G4G5G_en = 1;
g4g5g =
{
	func_en = 1;
	function = ( { title  = "4G/5G-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "4G/5G-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "4G/5G-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "4G/5G-PF-1";on_off = 1;times = 10;dura = 1;timeout = 1000; para_numb = 2;type1 = 0;param1 = 20;type2 = 0;param2 = 30;}
			);
			
	stab_en = 1;
	stability = ( { title  = "4G/5G-S-1";on_off = 1;times = 20;dura = 1;timeout = 600; para_numb = 0;},
					{ title  = "4G/5G-S-2";on_off = 1;times = 20;dura = 1;timeout = 600; para_numb = 0;},
					{ title  = "4G/5G-S-3";on_off = 1;times = 20;dura = 1;timeout = 600; para_numb = 0;},
					{ title  = "4G/5G-S-4";on_off = 1;times = 3;dura = 1;timeout = 600; para_numb = 0;},
          );
};

BLUETOOTH_en = 1;
bluetooth =
{
	func_en = 1;
	function = ( { title  = "BT-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "BT-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "BT-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 1;
	stability = ( { title  = "BT-S-1";on_off = 1;times = 1;dura = 43200;timeout = 60000; para_numb = 0;},
					{ title  = "BT-S-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
};

GPIO_en = 1;
gpio =
{
	func_en = 1;
	function = ( { title  = "GPIO-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "GPIO-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "GPIO-PF-3";on_off = 1;times = 5;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "GPIO-PF-4";on_off = 1;times = 5;dura = 1;timeout = 120; para_numb = 0;}
			);

	stab_en = 0;
};

PWM_en = 1;
pwm =
{
	func_en = 0;
	
	exp_func_en = 0;
	
	perf_en = 1;
	performance = ( { title  = "PWM-PF-1";on_off = 1;times = 1;dura = 120;timeout = 200; para_numb = 0;},
					{ title  = "PWM-PF-2";on_off = 1;times = 1;dura = 120;timeout = 200; para_numb = 0;}
			);

	stab_en = 1;
	stability = ( { title  = "PWM-S-1";on_off = 1;times = 1;dura = 120;timeout = 200; para_numb = 0;}
          );

};

WATCHDOG_en = 1;
watchdog =
{
	func_en = 1;
	function = ( { title  = "WD-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "WD-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "WD-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "WD-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "WD-F-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "WD-F-6";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "WD-F-7";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "WD-F-8";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 0;

};

CAMERA_en = 1;
camera =
{
	func_en = 1;
	function = ( { title  = "CAM-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAM-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAM-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "CAM-F-5";on_off = 1;times = 1;dura = 120;timeout = 180; para_numb = 0;},
				{ title  = "CAM-F-11";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 0;

};

AUDIO_en = 1;
audio =
{
	func_en = 1;
	function = ( { title  = "AUD-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "AUD-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "AUD-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "AUD-F-6";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 0;

};

RTC_en = 1;
rtc =
{
	func_en = 1;
	function = ( { title  = "RTC-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "RTC-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "RTC-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 0;

};

FS_en = 1;
filesystem =
{
	func_en = 1;
	function = ( { title  = "FS-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-7";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-8";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-9";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-10";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-11";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-12";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-13";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-14";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-15";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-16";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-17";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-18";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-19";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-20";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-21";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-22";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-23";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-24";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-25";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-26";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-27";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-28";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-29";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-30";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-31";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-32";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-33";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-34";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-35";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "FS-F-37";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 0;

};

OPENXX_en = 1;
openxx =
{
	func_en = 1;
	function = ( { title  = "OPEN-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "OPEN-F-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "OPEN-F-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "OPEN-F-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
				{ title  = "OPEN-F-6";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	
	stab_en = 0;

};

RTS_en = 1;
rts =
{
	func_en = 1;
	function = ( { title  = "RTS-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	performance = ( { title  = "RTS-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "RTS-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "RTS-PF-3";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "RTS-PF-4";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "RTS-PF-5";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
			);

	stab_en = 0;

};

ADC_en = 1;
adc =
{
	func_en = 1;
	function = ( { title  = "ADC-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	performance = ( { title  = "ADC-PF-2";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
			);

	stab_en = 0;

};

OTHER_en = 1;
other =
{
	func_en = 1;
	function = ( { title  = "OTHER-F-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;}
          );
		  
	exp_func_en = 0;
	
	perf_en = 0;
	performance = ( { title  = "OTHER-PF-1";on_off = 1;times = 1;dura = 1;timeout = 120; para_numb = 0;},
					{ title  = "OTHER-PF-4";on_off = 1;times = 600;dura = 1;timeout = 800; para_numb = 0;}
			);

	stab_en = 0;

};
