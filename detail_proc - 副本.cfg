##### stress test command list ###############################
#
# This file describes auto test command and resolution of the command result.
#
# Empty lines and lines starting with # are ignored
# Note: Please do not modify. 

#logical model : 0-uboot enter console 1-console wait time 2-uboot command interaction 3-sys command interaction
#                4-uboot watchdog  5-sys watchdog
#get result model : 0-After keyword x,y to atoi 1-After keyword x,y to atof 2-After keyword x,y string  3-full row 4-all 5-After keyword2 atoi 6-After keyword2 atof 7-After keyword2 str
# getvalue = keyword,value name,get result model,row(x),column(y)
# cmd_type : 0-tool 1-tester 2-Dual core communication
# data_type : 0-int 1-float 2-string

# The first string character for restart
# tester_power_path = "/sys/class/gpio/power12V/value";
# tty_dev = "/dev/ttyUSB0";
net_dev = "eth0";
enter_sys_time = 10;
first_str1 = "U-Boot SPL board init";
#first_str2 = ;

enter_console_step1 = "ETX";
enter_console_key1 = "0:Exit to console";
enter_console_step2 = "0";
enter_console_key2 = "=>";
exit_console = "reboot";

enter_key = "OK3568-buildroot";
user_name = "root";
pw_key = "Password";
password = "root";
login_str = "root@OK3568-buildroot:~#";
close_echo = "stty -F /dev/ttyFIQ0 -echo";

ftp_uname = "root";
ftp_pw = "root";

emmc_nand_name  = "mmcblk0";

# max case number
test_case_number = 7;

class_name = "XPU,BOOT,EMMC,NAND,SD/TF,SATA,DDR,SPI,I2C,CAN,UART,USB,PCIE,NET,WIFI,4G/5G,BT,GPIO,PWM,WD,CAM,AUD,RTC,FS,OPEN,RTS,ADC,OTHER";

# test item set.temp_item
test_case1_ =
{
	title  = "UART-F-1";
	model = 3;
	group1_ = ( { cmd_type = 0;cmd_content = "/auto/item/tty_test ttymxc2 -tool -check";},
				{ cmd_type = 1;cmd_content = "/auto/item/tty_test ttyS3 -tester -check &";ress = 3;res1_ = "check='N',1,1,1,PASSED,FAILED,@res1_";res2_ = "check='O',1,1,1,PASSED,FAILED,@res2_";res3_ = "check='E',1,1,1,PASSED,FAILED,@res3_";
	           res_reasons = 3;res_reason1_ = "check='N',7,0,0,res_reason=,,@res_reason1_";res_reason2_ = "check='O',7,0,0,res_reason=,,@res_reason2_";res_reason3_ = "check='E',7,0,0,res_reason=,,@res_reason3_";}	
          );
#	res_formula = "";
	res_formatting = "UART-F-1:3;No check,@res1_;odd check,@res2_;even check,@res3_;";
};

test_case2_ =
{
	title  = "UART-F-3";
	model = 3;
	group1_ = ( { cmd_type = 0;cmd_content = "/auto/item/tty_test ttymxc2 -tool -databit";},
				{ cmd_type = 1;cmd_content = "/auto/item/tty_test ttyS3 -tester -databit &";ress = 4;
				res1_ = "data_bit=6,1,1,1,PASSED,FAILED,@res1_";
				res2_ = "data_bit=7,1,1,1,PASSED,FAILED,@res2_";
				res3_ = "data_bit=8,1,1,1,PASSED,FAILED,@res3_";
				res4_ = "stop_bit=2,1,1,1,PASSED,FAILED,@res4_";
	           res_reasons = 4;
			   res_reason1_ = "data_bit=6,7,0,0,res_reason=,,@res_reason1_";
			   res_reason2_ = "data_bit=7,7,0,0,res_reason=,,@res_reason2_";
			   res_reason3_ = "data_bit=8,7,0,0,res_reason=,,@res_reason3_";
			   res_reason4_ = "stop_bit=2,7,0,0,res_reason=,,@res_reason4_";}	
          );
#	res_formula = "";
	res_formatting = "UART-F-3:4;bit6 stop1,@res1_;bit7 stop1,@res2_;bit8 stop1,@res3_;bit8 stop2,@res4_;";
};

test_case3_ =
{
	title  = "UART-F-5";
	model = 3;
	group1_ = ( { cmd_type = 0;cmd_content = "/auto/item/tty_test ttymxc2 -tool -bards";},
				{ cmd_type = 1;cmd_content = "/auto/item/tty_test ttyS3 -tester -bards &";ress = 10;
				res1_ = "baudrate=1200,1,1,1,PASSED,FAILED,@res1_";
				res2_ = "baudrate=2400,1,1,1,PASSED,FAILED,@res2_";
				res3_ = "baudrate=9600,1,1,1,PASSED,FAILED,@res3_";
				res4_ = "baudrate=115200,1,1,1,PASSED,FAILED,@res4_";
				res5_ = "baudrate=576000,1,1,1,PASSED,FAILED,@res5_";
				res6_ = "baudrate=1000000,1,1,1,PASSED,FAILED,@res6_";
				res7_ = "baudrate=1152000,1,1,1,PASSED,FAILED,@res7_";
				res8_ = "baudrate=2000000,1,1,1,PASSED,FAILED,@res8_";
				res9_ = "baudrate=3000000,1,1,1,PASSED,FAILED,@res9_";
				res10_ = "baudrate=4000000,1,1,1,PASSED,FAILED,@res10_";
	           res_reasons = 10;
			   res_reason1_ = "baudrate=1200,7,0,0,res_reason=,,@res_reason1_";
			   res_reason2_ = "baudrate=2400,7,0,0,res_reason=,,@res_reason2_";
			   res_reason3_ = "baudrate=9600,7,0,0,res_reason=,,@res_reason3_";
			   res_reason4_ = "baudrate=115200,7,0,0,res_reason=,,@res_reason4_";
			   res_reason5_ = "baudrate=576000,7,0,0,res_reason=,,@res_reason5_";
			   res_reason6_ = "baudrate=1000000,7,0,0,res_reason=,,@res_reason6_";
			   res_reason7_ = "baudrate=1152000,7,0,0,res_reason=,,@res_reason7_";
			   res_reason8_ = "baudrate=2000000,7,0,0,res_reason=,,@res_reason8_";
			   res_reason9_ = "baudrate=3000000,7,0,0,res_reason=,,@res_reason9_";
			   res_reason10_ = "baudrate=4000000,7,0,0,res_reason=,,@res_reason10_";
			   }	
          );
#	res_formula = "";
	res_formatting = "UART-F-5:10;1.2k,@res1_;2.4k,@res2_;9.6k,@res3_;115.2k,@res4_;576k,@res5_;1M,@res6_;1.152M,@res7_;2M,@res8_;3M,@res9_;4M,@res10_;";
};

test_case4_ =
{
	title  = "UART-PF-1";
	model = 3;
	group1_ = ( { cmd_type = 0;cmd_content = "/auto/item/tty_test ttymxc2 -tool -max_db";},
				{ cmd_type = 1;cmd_content = "/auto/item/tty_test ttyS3 -tester -max_db &";datas = 1;data1_ = "max baudrate=,6,0,0,max baudrate=,FAILED,@data1_";}	
          );
#	res_formula = "";
	data_formatting = "UART-PF-1:1;max baudrate,@data1_ k;";
};

test_case5_ =
{
	title  = "UART-PF-2";
	model = 3;
	group1_ = ( { cmd_type = 0;cmd_content = "/auto/item/tty_test ttymxc2 -l 8 -n -b 115200 -tool -tf";},
				{ cmd_type = 1;cmd_content = "/auto/item/tty_test ttyS3 -l 8 -n -b 115200 -tester -rf &";ress = 1;res1_ = "receive file test,1,1,1,PASSED,FAILED,@res1_";
				res_reasons = 1;res_reason1_ = "send file test,7,0,0,send file test,,@res_reason1_";}	
          );
#	res_formula = "";
	res_formatting = "UART-PF-2:1;send file test,@res1_;";
};

test_case6_ =
{
	title  = "UART-PF-3";
	model = 3;
	group1_ = ( { cmd_type = 0;cmd_content = "/auto/item/tty_test ttymxc2 -l 8 -n -b 115200 -tool -rf";},
				{ cmd_type = 1;cmd_content = "/auto/item/tty_test ttyS3 -l 8 -n -b 115200 -tester -tf &";ress = 1;res1_ = "send file test,1,1,1,PASSED,FAILED,@res1_";
				res_reasons = 1;res_reason1_ = "receive file test,7,0,0,receive file test,,@res_reason1_";}	
          );
#	res_formula = "";
	res_formatting = "UART-PF-3:1;receive file test,@res1_;";
};

test_case7_ =
{
	title  = "UART-PF-4";
	model = 3;
	group1_ = ( { cmd_type = 0;cmd_content = "/auto/item/tty_test ttymxc2 -l 8 -n -b 115200 -tool -rtf";},
				{ cmd_type = 1;cmd_content = "/auto/item/tty_test ttyS3 -l 8 -n -b 115200 -tester -rtf &";ress = 2;res1_ = "receive=,1,1,1,PASSED,FAILED,@res1_";res2_ = "send=,1,1,1,PASSED,FAILED,@res2_";
				res_reasons = 2;res_reason1_ = "receive=,7,0,0,res_reason=,,@res_reason1_";res_reason2_ = "send=,7,0,0,res_reason=,,@res_reason2_";}	
          );
#	res_formula = "";
	res_formatting = "UART-PF-4:2;r&t file receive test,@res1_;r&t file send test,@res2_;";
};

test_case20_ =
{
	title  = "WD-F-3";
	model = 4;
	group1_ = ( { cmd_type = 1;cmd_content = "printenv";ress = 1;res1_ = ",0,1,1,fl_wdt_en,,";},
				{ cmd_type = 1;cmd_content = "printenv";ress = 1;res1_ = ",0,1,1,fl_wdt_timeout,,";}
          );
	group2_ = ({ cmd_type = 1;cmd_content = "setenv fl_wdt_en 1";ress = 0;},
				{ cmd_type = 1;cmd_content = "setenv fl_wdt_timeout 10";ress = 0;},
				{ cmd_type = 1;cmd_content = "saveenv";ress = 0;}
          );
	group3_ = ({ cmd_type = 1;cmd_content = "setenv fl_wdt_en 0";ress = 0;},
				{ cmd_type = 1;cmd_content = "saveenv";ress = 0;}
          );
#	res_formula = "";
	res_formatting = "WD-F-3:1;@res1_;";
};
