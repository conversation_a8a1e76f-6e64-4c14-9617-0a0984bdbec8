
#ifndef MANAGE_H
#define MANA<PERSON>_H

#define MAX_CLASS 35
#define MAX_ITEM 70
#define MAX_ITEM1 20
#define MAX_GROUP 13
#define MAX_EXE_LINE 10
#define MAX_CMD_LEN 256
#define TEST_NAME_LEN 50
#define MAX_FORMULA_LEN 200
#define MAX_FORMATTING_LEN 1000
#define MAX_CONTENT_LEN 100
#define MAX_DATA_STR_LEN 265
#define MAX_RES_LEN 10
#define MAX_RES_REASON_LEN 100
#define MAX_SEND_LEN 1024
// #define MAX_SEND_DATA_LEN 70
#define MAX_SEND_QUEUE_LEN 100
// #define MAX_SEND_QUEUE_LEN 50
#define MAX_FAIL_PATH 50
#define MAX_STO_LEN 20
#define MAX_CLASS_LIST_LEN 20
#define MAX_KEEP_CHECK_STR_LEN 100
#define MAX_KEEP_CHECK_COUNT 10

#define CMD_LEN1 50
#define KEY_LEN1 50
#define KEY_LEN2 30
#define KEY_LEN3 40
#define PARAM_NUMB 10
#define KEY_NUMB 5
#define VALUE_NUMB 12
#define ERR_WORD_NUMB 5
#define SER_NUMB_LEN 50
#define MAX_LINE_LEN 255

#define MAX_FAC_NUMB 20
#define MAX_PLAT_NUMB 20
#define MAX_PLAT_LEN 40

#define MAX_RX_BUF_LEN 500 * 1024
#define MAX_TX_BUF_LEN 500
#define MAX_SYS_STATUS 7
#define MAX_BACKDATA_LEN 1024

#define HAVE_RESULT 1
#define EXE_CORRECT 0
#define TIMEOUT -1
#define HAVE_ERR_KW -2
#define enable 1
#define disable 0
#define CONTINUE_TEST 0
#define STOP_TEST 1
#define WAIT_RECOVER_TEST 2

#define READ 0
#define WRITE 1
#define MAX_EXE_ITEM 10
#define RES_TO_QUENE 1
#define DATA_TO_QUENE 2

#define SPECIAL_EXE 0
#define SPECIAL_CHECK 1

#define TESTER_IP_NOT_CHECK 0
#define TESTER_IP_OK 1
#define TESTER_IP_ERR 2

/*测试项枚举*/
typedef enum
{
    CLASS_XPU = 0,     /*XPU*/
    CLASS_BOOT = 1,    /*UBOOT*/
    CLASS_EMMC = 2,    /*EMMC*/
    CLASS_NAND = 3,    /*NAND*/
    CLASS_SD_TF = 4,   /*SD/TF*/
    CLASS_SATA = 5,    /*SATA*/
    CLASS_DDR = 6,     /*内存*/
    CLASS_SPI = 7,     /*SPI*/
    CLASS_I2C = 8,     /*I2C*/
    CLASS_CAN = 9,     /*CAN*/
    CLASS_CANFD = 10,  /*CANFD*/
    CLASS_UART = 11,   /*串口*/
    CLASS_USB = 12,    /*USB*/
    CLASS_PCIE = 13,   /*PCIE*/
    CLASS_NET = 14,    /*网络*/
    CLASS_WIFI = 15,   /*WIFI*/
    CLASS_G4G_5G = 16, /*4G/5G*/
    CLASS_BT = 17,     /*蓝牙*/
    CLASS_GPIO = 18,   /*GPIO*/
    CLASS_PWM = 19,    /*PWM*/
    CLASS_WD = 20,     /*看门狗*/
    CLASS_CAM = 21,    /*摄像头*/
    CLASS_TP = 22,     /*触摸*/
    CLASS_DISP = 23,   /*显示*/
    CLASS_AUD = 24,    /*音频*/
    CLASS_RTC = 25,    /*RTC*/
    CLASS_FS = 26,     /*文件系统*/
    CLASS_OPEN = 27,   /*开源框架*/
    CLASS_QT = 28,     /*QT*/
    CLASS_APP = 29,    /*应用*/
    CLASS_UPD = 30,    /*烧写*/
    CLASS_BPS = 31,    /*后备电源*/
    CLASS_RTS = 32,    /*实时系统*/
    CLASS_SAMP = 33,   /*采样*/
    CLASS_OTHER = 34,  /*其他*/

} enum_test_type;

/*系统状态*/
typedef enum
{
    STA_UNKNOW = 0,    /*系统状态不确定*/
    CONSOLE_MENU = 1,  /*控制台菜单*/
    CONSOLE_READY = 2, /*控制台已准备好*/
    LOG_OUT = 3,       /*未登录*/
    WAIT_PW = 4,       /*等待输入密码*/
    SYS_READY = 5,     /*系统已登录*/
    SYS_GDB = 6,       /*系统GDB交互*/
} enum_sys_status;

/*测试状态*/
typedef enum
{
    TEST_STEP_RUNNING = 1,         /*测试过程中*/
    TEST_STEP_STOP_TEST = 2,       /*停止测试*/
    TEST_STEP_SUSPENSION_TEST = 3, /*暂停测试*/
    TEST_STEP_MANUAL_TEST = 4,     /*手动测试*/
    TEST_STEP_GET_END_TEST = 5,    /*结束测试*/
} enum_test_status;

/*逻辑模型*/
typedef enum
{
    ENTER_CONSOLE_STR = 0,  /*正确进入控制台*/
    ENTER_CONSOLE_WAIT = 1, /*进入控制台等待时间*/
    CONSOLE_INTER = 2,      /*控制台命令交互*/
    SYS_INTER = 3,          /*文件系统命令交互*/
    CONSOLE_WATCHDOG = 4,   /*控制台看门狗*/
    SYS_WATCHDOG = 5,       /*系统看门狗*/
} enum_test_model;

/*读指针指到文件末尾枚举*/
typedef enum
{
    READ_POINT_NO_SYNC = 0,         /*不同步读指针*/
    READ_POINT_FIND_KW_CURRENT = 1, /*找到字符串后同步读指针到当前位置*/
    READ_POINT_FIND_KW_SYNC = 2,    /*找到字符串后同步读指针到文件结尾*/
    READ_POINT_NO_KW_SYNC = 3,      /*找不到字符串同步读指针到文件结尾*/
    READ_POINT_SYNC = 4,            /*同步读指针到文件结尾*/
} enum_test_sync;

/*获取数据类型*/
typedef enum
{
    STR2I = 0,                    /*关键字x,y后转为整形*/
    STR2F = 1,                    /*关键字x,y后转为浮点*/
    STRS = 2,                     /*关键字x,y后的字符串*/
    ONE_ROW = 3,                  /*关键字整行*/
    ALL = 4,                      /*所有信息*/
    KEYWORD2I = 5,                /*关键字后转整形*/
    KEYWORD2F = 6,                /*关键字后转浮点*/
    KEYWORD2_STR = 7,             /*关键字后字符串*/
    KEYWORD2I_ALL_SC_ERR = 8,     /*关键字后转整形,全部扫描错误关键字*/
    KEYWORD2F_ALL_SC_ERR = 9,     /*关键字后转浮点，全部扫描错误关键字*/
    KEYWORD2_STR_ALL_SC_ERR = 10, /*关键字后字符串，全部扫描错误关键字*/
} enum_get_value_model;

/*获取结果类型*/
typedef enum
{
    ALL_SCAN = 0,       /*全程扫描错误关键字和正确关键字*/
    KEYWORD_SCAN = 1,   /*关键字行扫描错误关键字和正确关键字*/
    ALL_SCAN_ERR_KW_OK, /*全程扫描错误关键字和关键字行扫描正确关键字*/
} enum_get_result_model;

/*测试区域类型*/
typedef enum
{
    FUNC = 0,         /*功能测试*/
    EXP_FUNC = 1,     /*扩展功能测试*/
    PERF = 2,         /*性能测试*/
    STAB = 3,         /*稳定性测试*/
    OTHER_REGION = 4, /*其他测试*/
} enum_region_type;

/*数据类型*/
typedef enum
{
    DATA_INT = 0,   /*整形*/
    DATA_FLOAT = 1, /*浮点型*/
    DATA_STR = 2,   /*字符串类型*/
} enum_data_type;
/*CMD类型*/
typedef enum
{
    CMD_TOOL = 0,    /*工装执行*/
    CMD_TESTER = 1,  /*被测执行*/
    CMD_SPECIAL = 2, /*特殊命令*/
} enum_cmd_type;
/*网口线程通道交互信息*/
typedef enum
{
    NET_QUITE_EVENT = 1,        /*线程退出主线程让网络线程退出*/
    NET_CONNECT_OK = 2,         /*连接成功*/
    NET_CONNECT_FAIL = 3,       /*连接失败*/
    NET_THREAD_QUITE = 4,       /*线程退出，网络线程主动退出*/
    NET_SEND_FAIL = 5,          /*发送失败*/
    NET_NORMAL_QUITE_EVENT = 6, /*测试完成，正常退出网络线程*/

    NET_SEND_ID = 11,                     /*发送ID*/
    NET_SEN_TESTER_INFO = 12,             /*发送被测信息*/
    NET_SEND_TESTER_CHG_CNTE_OR_NOT = 13, /*发送被测设备已更换，是否继续测试*/
    NET_SEND_REQUST_COUNTINUE_TEST = 14,  /*请求继续测试*/
    NET_SEND_PARAM_OK = 15,               /*发送参数正确*/
    NET_SEND_PARAM_ERR = 16,              /*发送参数错误*/
    NET_SEND_TEST_END = 17,               /*发送测试结束*/
    NET_SEND_RES = 18,                    /*发送结果*/
    NET_SEND_DATA = 19,                   /*发送数据*/
    NET_SEND_HUM_OPER = 20,               /*发送人需要操作*/
    NET_SEND_HUM_CONFIRM = 21,            /*发送人需要确认*/
    NET_SEND_HEARTBEAT = 22,              /*发送心跳*/

    NET_BACK_ACK = 21,               /*返回ACK*/
    NET_BACK_ID_ERR = 22,            /*返回ID不在列表*/
    NET_BACK_START_TEST = 23,        /*返回开始测试*/
    NET_BACK_STOP_TEST = 24,         /*返回停止测试*/
    NET_BACK_RESTART_TEST = 25,      /*返回重新测试*/
    NET_BACK_SUSPENSION_TEST = 26,   /*返回暂停测试*/
    NET_BACK_CONTINUE_TEST = 27,     /*返回继续测试*/
    NET_BACK_SKIP_CURRENT_TEST = 28, /*返回停止当前项测试，开始下一项*/
    NET_BACK_PARAM_PATH = 29,        /*返回测试参数路径*/
    NET_BACK_GET_ID = 30,            /*返回获取设备ID命令*/
    NET_BACK_HUM_OPER_OK = 31,       /*返回人操作完成,给测试线程*/
    NET_BACK_HUM_OPER_NG = 32,       /*返回人操作完成,给测试线程*/
    NET_BACK_HUM_CONFIRM_OK = 33,    /*人反馈结果正常,给测试线程*/
    NET_BACK_HUM_CONFIRM_NG = 34,    /*人反馈结果异常,给测试线程*/

    NET_ERR_A_BASE = 100, /*A类错误基数*/
    NET_ERR_A1 = 101,     /*打不开被测设备调试串口,测试已终止*/
    NET_ERR_A2 = 102,     /*被测设备上电异常，测试已终止*/
    NET_ERR_A3 = 103,     /*设备无法进入系统，请检查用户名，密码，测试已终止*/
    NET_ERR_A4 = 104,     /*匹配不到被测平台信息，测试已终止*/
    NET_ERR_A5 = 105,     /*无法获取被测工具包或详细参数，测试已终止*/
    NET_ERR_A6 = 106,     /*被测设备无法获取工具包，请检查网线后复测，测试已终止*/
    NET_ERR_A7 = 107,     /*工装异常退出，请给工装重新断电再上电*/
    NET_ERR_A8 = 108,     /*工装续测参数错误，工装已重新开始测试*/
    NET_ERR_A9 = 109,     /*被测设备测试过程中无法启动，测试已终止*/
    NET_ERR_A10 = 110,    /*工装测试参数错误，测试已终止*/
    NET_ERR_B_BASE = 200, /*B类错误基数*/
    NET_ERR_B1 = 201,     /*被测设备异常重启*/
    NET_ERR_B2 = 202,     /*被测设备I/O错误*/
    NET_ERR_B3 = 203,     /*被测设备内存错误*/
    NET_ERR_B4 = 204,     /*被测设备测试过程中无法启动*/
    NET_ERR_B5 = 205,     /**/
    NET_ERR_B6 = 206,     /**/
    NET_ERR_B7 = 207,     /**/

} enum_net_socketpair_type;

/*串口线程通道交互信息*/
typedef enum
{
    TTY_QUITE_EVENT = 1, /*线程退出*/
} enum_tty_socketpair_type;
/*测试管理线程通道交互信息*/
typedef enum
{
    MANAGE_QUITE_EVENT = 1,     /*线程退出*/
    MANAGE_TEST_COMPLETED = 2,  /*该项测试完成，包含正常和异常*/
    MANAGE_SUSPENSION_TEST = 3, /*暂停测试*/
    MANAGE_SKIP_TEST = 4,       /*跳过该项测试*/
    MANAGE_STOP_TEST = 5,       /*停止测试*/
} enum_manage_socketpair_type;

/*一问一答*/
typedef struct
{
    char cmd[CMD_LEN1];
    char key[KEY_LEN1];
} struct_one_cmd_one_key;

/*数据结构体*/
typedef struct
{
    enum_data_type type;             /*数据类型*/
    int data_int;                    /*整形数据*/
    float data_float;                /*浮点数据*/
    char data_str[MAX_DATA_STR_LEN]; /*字符串内容*/
} struct_data_class;

/*结果结构体*/
typedef struct
{
    char res[MAX_RES_LEN];               /*pass or fail*/
    char res_reason[MAX_RES_REASON_LEN]; /*失败原因*/
} struct_res_class;

/*测试项字符串和枚举对应*/
typedef struct
{
    enum_test_type numbs;    /*测试类别*/
    char str[TEST_NAME_LEN]; /*字符串内容*/
} struct_name_str2numb;
typedef struct
{
    char key_word[KEY_LEN1];    /*查找关键字*/
    enum_get_value_model modle; /*模式*/
    int x;                      /*第几行*/
    int y;                      /*第几列*/
    char key_word2[KEY_LEN1];   /*查找关键字*/
    char err_word[KEY_LEN1];    /*查找关键字*/
    char not_word[KEY_LEN1];    /*有关键字，但是不能有not 关键字*/
    char storage[MAX_STO_LEN];  /*存储位置*/
    int flag;                   /*是否有结果*/
} struct_get_value_form;
typedef struct
{
    char key_word[KEY_LEN1];                /*查找关键字*/
    enum_get_result_model modle;            /*模式*/
    int kw_count;                           /*关键字个数*/
    int ew_count;                           /*错误关键字个数*/
    char pass_word[KEY_NUMB][KEY_LEN1];     /* 查找关键字 */
    char err_word[ERR_WORD_NUMB][KEY_LEN1]; /* 错误关键字 */
    char storage[MAX_STO_LEN];              /*存储位置*/
    int flag;                               /*是否有结果*/
} struct_get_result_form;

/*
typedef struct
{
    char content[MAX_CMD_LEN]; //命令内容
    enum_cmd_type flag; //标志0-给工装 1-给设备 2-双核通信
}
struct_test_cmd;
*/
/*每条测试命令结构体*/
typedef struct
{

    char content[MAX_CMD_LEN]; /*命令内容*/
    enum_cmd_type flag;        /*标志0-给工装 1-给设备 2-双核通信*/
    int status;                /*执行状态0-未执行完 1-已执行完*/
    int pid;                   /*进程号*/
    // unsigned long start_exe_time; /*开始执行时间*/
    // int exe_time;                 /*执行时间*/

    struct_get_value_form get_data[VALUE_NUMB];       /* 获取数据格式 */
    int data_count;                                   /*数据个数*/
    struct_get_result_form get_res[VALUE_NUMB];       /*获取结果格式 */
    int res_count;                                    /*结果个数*/
    struct_get_value_form get_res_reason[VALUE_NUMB]; /*获取错误原因格式 */
    int res_reason_count;                             /*结果原因个数*/
} struct_test_line;

/*一个测试组测试行结构体*/
typedef struct
{
    struct_test_line line[MAX_EXE_LINE]; /* 测试命令集 */
    int step;                            /*执行到第几步*/
    int line_count;                      /* 一组最多15条命令，此处记录实际多少条 */
} struct_test_group;

/*skip end pid 记录*/
typedef struct
{
    int pid_num;
    int pid[MAX_EXE_LINE];
} struct_skip_end_pid;

/*每个测试条目结构体*/
typedef struct
{
    int item_en;                  /*0-不执行 1-执行*/
    char title[SER_NUMB_LEN];     /* 当前测试对应测试编号*/
    unsigned long all_start_time; /*本条测试开始时间*/
    unsigned long one_start_time; /*本轮测试开始时间*/
    unsigned long process_time;   /*过程时间记录*/
    int times;                    /*测试次数*/
    int term_test;                /*是否终止测试*/
    // int times_cur;                       /*当前测试第几次*/
    int dura;                            /*持续时间*/
    int timeout;                         /*超时时间*/
    int param_count;                     /*参数个数*/
    struct_data_class param[PARAM_NUMB]; /*参数*/

    enum_test_model modle;              /*逻辑模型*/
    struct_test_group group[MAX_GROUP]; /* 一组测试 */
    int group_count;                    /* 一条测试最多10组，此处记录实际多少组 */
    int group_cur;                      /*当前执行第几组*/
    int group_recover;                  /*0-没有回收组 非零值为需要做回收的组 */

    // int res_count;                      /*结果个数*/
    struct_res_class res[VALUE_NUMB]; /*结果*/
    // int data_count;                     /*数据个数*/
    struct_data_class data[VALUE_NUMB]; /*数据*/
    struct_skip_end_pid skip_end_pid;

    char res_formula[MAX_FORMULA_LEN];        /*结果公式，可多个，用分号隔开*/
    char res_formatting[MAX_FORMATTING_LEN];  /*结果格式化*/
    char data_formatting[MAX_FORMATTING_LEN]; /*数据格式化*/

} struct_test_item;

/*每个测试组结构体*/
typedef struct
{
    int region_en;                   /*0-不执行 1-执行*/
    struct_test_item item[MAX_ITEM]; /*一条测试*/
    int item_count;                  /* 一组最多80个，此处记录实际多少组 */
    int item_cur;                    /*当前执行第几组*/
} struct_test_region;

/*每个测试组结构体*/
typedef struct
{
    int region_en;                    /*0-不执行 1-执行*/
    struct_test_item item[MAX_ITEM1]; /*一条测试*/
    int item_count;                   /* 一组最多20个，此处记录实际多少组 */
    int item_cur;                     /*当前执行第几组*/
} struct_test_region1;

/*每个测试项结构体*/
typedef struct
{
    int class_en;                     /*0-不执行 1-执行*/
    struct_test_region function;      /*功能测试项*/
    struct_test_region1 exp_function; /*扩展功能测试项*/
    struct_test_region1 performance;  /*性能测试项*/
    struct_test_region1 stab;         /* 稳定性测试项 */
    // struct_test_region other;        /* 其他测试项 */
    enum_region_type region_cur; /* 当前测试区域 */

} struct_test_class;
/*进出控制台*/
typedef struct
{
    struct_one_cmd_one_key step[2];
    struct_one_cmd_one_key exit;
} struct_console;

/*起机相关*/
typedef struct
{
    char first_strs[2][KEY_LEN1];
    char enter_key[KEY_LEN2];
    char user_name[KEY_LEN1];
    char pw_key[KEY_LEN1];
    char pass_word[KEY_LEN1];
    char login_str[KEY_LEN2];
    int time;
    char emmc_dev[KEY_LEN1];
    char ftp_uname[KEY_LEN1];
    char ftp_pw[KEY_LEN1];
    char close_echo[KEY_LEN3];
    int need_init_tester;
} struct_sys_start;

/*整体测试结构体*/
typedef struct
{
    struct_test_class **test_class;    /*测试命令管理 - 改为指针数组*/
    int class_count;                    /* 一共多少个测试项 */
    int class_cur;                      /*当前执行第几个测试项*/
    unsigned long start_time;           /*本轮测试开始时间*/
    enum_test_status test_status;       /*测试状态*/
    struct_console console;
    struct_sys_start sys_start;
    char dev[20]; /*设备名称*/
    char net_dev[20];
    char tester_power_path[50];
    char app_path[50];
} struct_test;

/*信息结构体*/
typedef struct
{
    char platform[MAX_PLAT_LEN];       /*平台*/
    char factory[MAX_PLAT_LEN];        /*厂家*/
    char kernel_version[MAX_PLAT_LEN]; /*内核版本*/
    char uboot_version[MAX_PLAT_LEN];  /*uboot版本*/
    float mem_size;                    /*内存大小 单位G*/
    float flash_size;                  /*flash大小 单位G*/
    int tester_ip_ok;                  /*被测设备IP正确*/

} struct_info;

/*功能模块列表结构体*/
typedef struct
{
    char strs[MAX_CLASS][MAX_CLASS_LIST_LEN];

} struct_class_list;

/*厂家列表结构体*/
typedef struct
{
    char fact_name[MAX_PLAT_LEN];
    char plat_name[MAX_PLAT_NUMB][MAX_PLAT_LEN];
} struct_factorys;

/*厂家和平台*/
typedef struct
{
    struct_factorys factorys[MAX_FAC_NUMB];

} struct_all_factory_list;

/*测试数据结构体*/
typedef struct
{
    char strs[VALUE_NUMB][MAX_LINE_LEN];

} struct_test_result;

/*重发数据结构体*/
typedef struct
{
    char data[MAX_BACKDATA_LEN];
    int len;
    int resend_flag;
} struct_resend;

/*测试数据给网络发送结构体*/
typedef struct
{
    char data[MAX_SEND_LEN]; // 要发送的数据
    int flag;                // 是否有有效数据，0-无效 1-有效
    int frame_flag;          // 是否分帧0-不分 1-分且后续还有帧 2-分帧且为最后一帧
    int frame_num;           // 帧序号
} struct_send_to_net;
typedef struct
{
    struct_send_to_net frame[MAX_SEND_QUEUE_LEN]; // 要发送的数据
    int read_point;                               // 读指针
    int write_point;                              // 写指针
} struct_frame_queue;

typedef struct
{
    char keep_check_str[MAX_KEEP_CHECK_COUNT][MAX_KEEP_CHECK_STR_LEN];
    int keep_check_count;
} struct_keep_check;

// 全局变量
extern int tty_fd;
extern int tty_receive_signal_fd[];
extern int net_inter_signal_fd[];
extern int test_manage_signal_fd[];
extern int manage_net_signal_fd[];
extern pthread_t log_save_thread;
extern pthread_t test_manage_thread;
extern pthread_t net_inter_thread;
extern char info_file[];
extern char local_ip[];
extern char remote_ip[];
extern char conf_file[];
extern char param_file[];
extern char param_file_md5[];
extern char platform_file[];
extern struct_send_to_net send_to_net;
extern struct_test *test;
extern struct_frame_queue *frame_queue;
extern pthread_mutex_t mutex; // 串口接收锁访问文件锁
extern long long read_point;
extern char tool_ID[];
extern struct_info info;
extern char put_log_path[];
extern int save_no_time;
extern struct_keep_check keep_check;
extern char human_operation[];
// 函数声明
unsigned long func_get_system_time_ms(void);
void *tty_receive_manage(void *arg);
int func_test_class_func_perf(struct_test *test_para, int class);
int func_test_class_stab(struct_test *test_para, int class);
void file_reorg_data(FILE *pfile);
int func_test_prepare(struct_test *manag, struct_info *get_info, int flag);
void *net_inter_manage(void *arg);
void *func_one_item_test_thread(void *arg);
int func_get_platform(struct_info *info);
int bsp_get_value(FILE *src_File, long long src_read_point, struct_get_value_form *from, struct_data_class *data);
void bsp_sync_read_point(void);
void main_send_event_to_net_inter(int event);
void main_send_event_to_test_manage(int event);

// 内存管理函数声明
int func_init_test_memory(struct_test *test);
void func_free_test_memory(struct_test *test);
void test_manage_send_event_to_main(int event);
void net_inter_send_event_to_main(int event);
void net_send_event_to_test_manage(int event);
void test_manage_send_event_to_net(int event);
int bsp_check_keyword(char *str, enum_test_sync sync_read_point);
int func_makesure_sys_start(struct_sys_start *start_info, int timeout);
int func_login_sys(struct_sys_start *start_info);
int func_put_f_to_ssh_server(const char *local_file, const char *remote_user, const char *remote_host, const char *remote_path, const char *password);
void func_set_gpio_sta(char *path, int sta);
void bsp_lcd_display(const char *str);
int func_write_test_step(int mode, int item_class, int item_region, int item_item);
int func_get_f_from_ssh_server(const char *local_file, const char *remote_user, const char *remote_host, const char *remote_path, const char *password);
int func_get_f_from_lftp_server(const char *local_file, const char *remote_user, const char *remote_host, const char *remote_path, const char *password);
#endif