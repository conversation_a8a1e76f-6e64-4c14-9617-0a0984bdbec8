#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <termios.h> /*PPSIX 终端控制定义*/
#include <signal.h>
#include <pthread.h>
#include "file.h"
#include "config_file.h"
#include "pidfile.h"
#include "manage.h"
#include "serial.h"

// 版本
char ver[20] = {"ver01.002"}; // 测试程序版本
// 测试用结构体
struct_test *test;
struct_frame_queue *frame_queue;
struct_info info;
struct_keep_check keep_check;
char local_ip[20] = {0};
char remote_ip[20] = {0};
char tool_ID[20] = {0};
char put_log_path[230] = {0};

// 日志及结果文件
FILE *pLogFile = NULL, *presultFile = NULL;
char log_file[100] = {"/auto/log/records.log"};
char log_reorg_file[100] = {"/auto/log/records_reorg.log"};
char result_file[100] = {"/auto/log/result.csv"};
char self_log_file[100] = {"/auto/log/self_log_file.log"};
char pid_file[100] = {"/run/auto_test.pid"};
char conf_file[100] = {"/auto/detail_proc.cfg"};
char param_file[100] = {"/auto/param.cfg"};
char param_file_md5[32] = {0};
char platform_file[100] = {"/auto/platform.cfg"};
char info_file[100] = {"/auto/info.txt"};

// 串口交互相关线程
pthread_t log_save_thread;
long sync_len = 0;
int tty_receive_signal_fd[2];
int tty_fd;

// 网络交互相关线程
pthread_t net_inter_thread;
int net_inter_signal_fd[2];

// 测试项管理线程
pthread_t test_manage_thread;
int test_manage_signal_fd[2];
int continue_test_flag = 0;
int manage_net_signal_fd[2];

/*
 * @description : 自定义打印函数
 * @param - buff: 打印数据缓冲区
 * @param - lens: 打印数据长度
 * @param - mode: 打印格式
 * @return		: 无
 */
void func_my_print(char *buff, unsigned int lens, unsigned char mode)
{
    int i = 0;

    switch (mode)
    {
    case 'H': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("0X%02X  ", buff[i]);
        }
        break;
    case 'h': // 按照16进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("0x%02x  ", buff[i]);
        }
        break;
    case 'd': // 按照10进制打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%02d  ", buff[i]);
        }
        break;
    case 'c': // 按照字符打印数据
        for (i = 0; i < lens; i++)
        {
            printf("%c", buff[i]);
        }
        break;
    default:
        break;
    }
    printf("\n");
}
/*
 * @description    : 给串口线程发送信号,主要用途是通知线程回收资源
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void main_send_event_to_tty_receive(int event)
{
    if (write(tty_receive_signal_fd[0], &event, sizeof(event)) == -1)
    {
        printf("send signal to tty receive err\n");
    };
}
/*
 * @description    : 给网络线程发送信号,主要用途是通知线程回收资源
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void main_send_event_to_net_inter(int event)
{
    if (write(net_inter_signal_fd[0], &event, sizeof(event)) == -1)
    {
        printf("send signal to net inter err\n");
    };
}
/*
 * @description    : 给网络线程发送信号,主要用途是通知主流程有命令
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void net_inter_send_event_to_main(int event)
{
    printf("net_inter_send_event_to_main event=%d\n", event);
    if (write(net_inter_signal_fd[1], &event, sizeof(event)) == -1)
    {
        printf("net send signal to main err\n");
    };
}
/*
 * @description    : 给测试线程发送信号,主要用途是通知线程回收资源
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void main_send_event_to_test_manage(int event)
{
    if (write(test_manage_signal_fd[0], &event, sizeof(event)) == -1)
    {
        printf("send signal to test manage err\n");
    };
}
/*
 * @description    : 给测试管理线程发送信号,主要用途是通知主流程有测试完成
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void test_manage_send_event_to_main(int event)
{
    if (write(test_manage_signal_fd[1], &event, sizeof(event)) == -1)
    {
        printf("test manage send signal to main err\n");
    };
}
/*
 * @description    : 网络发信号给测试管理线程
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void net_send_event_to_test_manage(int event)
{
    if (write(manage_net_signal_fd[0], &event, sizeof(event)) == -1)
    {
        printf("net send signal to test manage err\n");
    };
}
/*
 * @description    : 测试管理线程发信号给网络线程
 * @param - event  : 信号
 * @return		   : 执行结果
 */
void test_manage_send_event_to_net(int event)
{
    if (write(manage_net_signal_fd[1], &event, sizeof(event)) == -1)
    {
        printf("test manage send signal to net err\n");
    };
}

/*
 * @description    : 信号处理函数
 * @param - iSignNo: 信号
 * @return		   : 执行结果
 */
void SignHandler(int iSignNo)
{
    if (net_inter_thread)
    {
        main_send_event_to_net_inter(NET_QUITE_EVENT);
    }
    if (log_save_thread)
    {
        main_send_event_to_tty_receive(TTY_QUITE_EVENT);
    }
    if (test_manage_thread)
    {
        main_send_event_to_test_manage(MANAGE_QUITE_EVENT);
    }
    sleep(3);
    if (tty_fd)
        close(tty_fd);
    fflush(pLogFile); /*将数据同步至ROM*/
    file_reorg_data(pLogFile);
    file_close(pLogFile); /*关闭文件*/
    file_close(presultFile);
    remove_pid(pid_file);
    if (net_inter_signal_fd[0])
        close(net_inter_signal_fd[0]);
    if (net_inter_signal_fd[1])
        close(net_inter_signal_fd[1]);
    if (test_manage_signal_fd[0])
        close(test_manage_signal_fd[0]);
    if (test_manage_signal_fd[1])
        close(test_manage_signal_fd[1]);
    if (tty_receive_signal_fd[0])
        close(tty_receive_signal_fd[0]);
    if (tty_receive_signal_fd[1])
        close(tty_receive_signal_fd[1]);
    if (manage_net_signal_fd[0])
        close(manage_net_signal_fd[0]);
    if (manage_net_signal_fd[1])
        close(manage_net_signal_fd[1]);

    func_set_gpio_sta(test->tester_power_path, 0);
    free(test);
    free(frame_queue);

    exit(0);
}
/*
 * @description  : 打印参数设置格式
 * @param - pname: 函数名
 * @return		 : 无
 */
static void print_usage(const char *pname)
{
    printf("Usage: %s [-f flag] [-v]"
           "\n\t'-f flag' for 0 is re-test, 1 is continue test"
           "\n\t'-v' show version"
           "\n\texample :  --> ./auto_test -f 0\n ",
           pname);
}
/*
 * @description  : LCD显示测试结果
 * @param - str  : 显示内容
 * @return		 : 无
 */
void bsp_lcd_display(const char *str)
{
    char str1[100] = {0};
    sprintf(str1, "i2c_display_test -d i2c-1 -s %s &", str);
    system("pkill -f \"i2c_display_test -d i2c-1\"");
    system(str1);
}

/*
 * @description   : 解析函数带入参数
 * @param - numb  : 参数个数
 * @param - *param: 带入参数数组指针
 * @return		  : 无
 */
void get_param(int numb, char *param[])
{
    int i = 0;

    // strcpy(conf_file, param[1]);

    if (numb <= 1)
        return;

    for (i = 1; i < numb; i++)
    {
        if (!strcmp(param[i], "-f"))
        {
            i++;
            continue_test_flag = atoi(param[i]);
            continue;
        }
        if (!strcmp(param[i], "-l_ip"))
        {
            i++;
            strcpy(local_ip, param[i]);
            continue;
        }
        if (!strcmp(param[i], "-r_ip"))
        {
            i++;
            strcpy(remote_ip, param[i]);
            continue;
        }
        if (!strcmp(param[i], "-id"))
        {
            i++;
            strcpy(tool_ID, param[i]);
            continue;
        }
        if (!strcmp(param[i], "-v"))
        {
            printf("auto_test ver:  %s\n", ver);
            continue;
        }
    }
}

/*
 * @description  : 主函数
 * @param - argc : 参数个数
 * @param - *argv: 带入参数数组指针
 * @return		 : 执行结果
 */
int main(int argc, char *argv[])
{
    int class = 0, result = 0;
    int triger_event = 0;
    int stab_flag = 0; // 0-功能性能测试，1-稳定性测试
    unsigned long len = 0;
    char tar_name[100] = {0};
    char temp_buff[300] = {0};
    int len_temp = 0;
    char get_picture[200] = {0};

    // 所有参数和状态清0
    len = sizeof(struct_test);
    printf("struct_test=%lu\n", len);
    unsigned long len1 = sizeof(struct_frame_queue);
    printf("struct_frame_queue=%lu\n", len1);
    unsigned long len2 = sizeof(struct_test_class);
    printf("struct_test_class=%lu\n", len2);
    unsigned long len3 = sizeof(struct_test_region);
    printf("struct_test_region=%lu\n", len3);
    unsigned long len4 = sizeof(struct_test_region1);
    printf("struct_test_region1=%lu\n", len4);
    unsigned long len5 = sizeof(struct_test_item);
    printf("struct_test_item=%lu\n", len5);
    unsigned long len6 = sizeof(struct_test_group);
    printf("struct_test_group=%lu\n", len6);
    unsigned long len7 = sizeof(struct_test_line);
    printf("struct_test_line=%lu\n", len7);
    test = (struct_test *)calloc(1, sizeof(struct_test));
    frame_queue = (struct_frame_queue *)calloc(1, sizeof(struct_frame_queue));
    printf("test=%p, frame_queue=%p\n", test, frame_queue);
    if ((NULL == test) || (NULL == frame_queue))
    {
        printf("calloc error\n");
        return -1;
    }

    // 初始化测试类内存
    if (func_init_test_memory(test) != 0)
    {
        printf("init test memory error\n");
        free(test);
        free(frame_queue);
        return -1;
    }

    memset((char *)&info, 0, sizeof(struct_info));
    memset((char *)&keep_check, 0, sizeof(struct_keep_check));

    // 检测是否有-h或--help
    if (argc > 1)
    {
        if ((!strcmp(argv[1], "-h")) || (!strcmp(argv[1], "--help")))
        {
            print_usage(argv[0]);
            exit(1);
        }
    }
    // 参数解析
    get_param(argc, argv);

    // 确认进程是否已经运行
    pid_t ppid = getpid();
    if (!check_pid(pid_file))
    {
        printf("pid_file=%s\n", pid_file);
        if (!write_pid(pid_file))
        {
            printf("write_file ok\n");
            if (getpid() != ppid)
                kill(ppid, SIGTERM);
            exit(1);
        }
    }
    else
    {
        // 没有此pid文件或此pid文件不是auto_test进程
        if (0 == check_old_pid(pid_file))
        {
            // 删除旧pid文件，写新的pid文件
            remove_pid(pid_file);
            if (!write_pid(pid_file))
            {
                if (getpid() != ppid)
                    kill(ppid, SIGTERM);
                exit(1);
            }
        }
        else
        {
            printf("auto_test: Already running.\n");
            if (getpid() != ppid)
                kill(ppid, SIGTERM);
            exit(1);
        }
    }

    // 保存日志文件
    pLogFile = file_open(log_file, WTWR);
    if (NULL == pLogFile)
        exit(1);
    presultFile = file_open(result_file, WTWR);
    if (NULL == presultFile)
        exit(1);

    // 进程被kill 需要保存文件
    signal(SIGTERM, SignHandler);
    // 按下 ctrl + c 需要保存文件
    signal(SIGINT, SignHandler);

    bsp_lcd_display("正在初始化中!!!");

    // 创建主流程和网络交互信号通道
    if (socketpair(AF_LOCAL, SOCK_STREAM, 0, net_inter_signal_fd) < 0)
    {
        printf("%s Faild to create net_inter_signal_fd: %d (%s)", __func__, errno, strerror(errno));
        goto __ToEnd;
    }
    // 创建主流程和测试管理信号通道
    if (socketpair(AF_LOCAL, SOCK_STREAM, 0, test_manage_signal_fd) < 0)
    {
        printf("%s Faild to create test_manage_signal_fd: %d (%s)", __func__, errno, strerror(errno));
        goto __ToEnd;
    }
    // 创建主流程和串口交互信号通道
    if (socketpair(AF_LOCAL, SOCK_STREAM, 0, tty_receive_signal_fd) < 0)
    {
        printf("%s Faild to create tty_receive_signal_fd: %d (%s)", __func__, errno, strerror(errno));
        goto __ToEnd;
    }
    // 创建主流程和串口交互信号通道
    if (socketpair(AF_LOCAL, SOCK_STREAM, 0, manage_net_signal_fd) < 0)
    {
        printf("%s Faild to create manage_net_signal_fd: %d (%s)", __func__, errno, strerror(errno));
        goto __ToEnd;
    }

    // 创建网络交互线程
    if (pthread_create(&net_inter_thread, NULL, net_inter_manage, NULL))
    {
        printf("Pthread net_inter_manage create error\n");
        goto __ToEnd;
    }
    // 确定网络连接是否成功
    struct timeval tv;
    tv.tv_sec = 30; // 设置超时时间为30秒
    tv.tv_usec = 0;
    setsockopt(net_inter_signal_fd[0], SOL_SOCKET, SO_RCVTIMEO, (const char *)&tv, sizeof tv);
    if (read(net_inter_signal_fd[0], &triger_event, sizeof(triger_event)) != sizeof(triger_event) || (triger_event != NET_CONNECT_OK))
    {
        printf("net connect fail\n");
        bsp_lcd_display("网络连接失败!!!");
        goto __ToEnd;
    }
    // 发送ID
    main_send_event_to_net_inter(NET_SEND_ID);
    if ((read(net_inter_signal_fd[0], &triger_event, sizeof(triger_event)) == sizeof(triger_event)))
    {
        if (triger_event == NET_BACK_ID_ERR)
        {
            printf("ID is not in the list\n");
            bsp_lcd_display("编号不在列表,联系管理员添加编号");

            goto __ToEnd;
        }
        if (triger_event == NET_BACK_ACK)
        {
            printf("ID is OK\n");
        }
    }
    else
    {
        printf("net inter fail\n");
        bsp_lcd_display("无法与服务器交互");
        goto __ToEnd;
    }

    // 测试准备阶段
    result = func_test_prepare(test, &info, continue_test_flag);
    if (-1 == result)
    {
        goto __ToEnd;
    }
    // 测试开始
    test->start_time = func_get_system_time_ms();
    bsp_lcd_display("正在测试中!!!");
    // 继续测试判别
    if (STAB == test->test_class[test->class_cur]->region_cur)
    {
        stab_flag = 1;
    }
    // 功能，扩展功能，性能测试
    class = test->class_cur;
    if (0 == stab_flag)
    {
        for (; class < test->class_count; class++)
        {
            result = func_test_class_func_perf(test, class);
            if (-1 == result)
                goto __BeforeEnd;
        }
        class = 0;
    }
    // 稳定性测试
    for (; class < test->class_count; class++)
    {
        result = func_test_class_stab(test, class);
        if (-1 == result)
            goto __BeforeEnd;
    }
__BeforeEnd:
    bsp_lcd_display("测试已经结束");
    // 打包测试记录，上送到服务器
    sprintf(get_picture, "%s/item/20-CAM/result/picture/", test->app_path);
    func_get_f_from_ssh_server("/auto/log/", test->sys_start.ftp_uname, "192.168.0.101", get_picture, test->sys_start.ftp_pw);
    strcpy(tar_name, tool_ID);
    len_temp = strlen(tar_name);
    tar_name[len_temp] = '_';
    Func_Time_GetSystemTime_ToChar1(&tar_name[(len_temp + 1)]);
    strcat(tar_name, ".tar");
    sprintf(temp_buff, "tar -cvf /auto/%s -C /auto log/", tar_name);
    system(temp_buff);
    memset(temp_buff, 0, sizeof(temp_buff));
    sprintf(temp_buff, "/auto/%s", tar_name);
    sprintf(put_log_path, "/report/%s/%s/%s/%s", info.factory, info.platform, info.kernel_version, tar_name);
    printf("put_log_path=%s\n", put_log_path);
    func_put_f_to_ssh_server(temp_buff, "forFTP", remote_ip, put_log_path, "123456");
    main_send_event_to_net_inter(NET_SEND_TEST_END);
    fflush(stdout);
    sleep(5);
__ToEnd:
    printf("%s exit\n", __func__);
    if (net_inter_thread)
    {
        main_send_event_to_net_inter(NET_NORMAL_QUITE_EVENT);
    }
    if (log_save_thread)
    {
        main_send_event_to_tty_receive(TTY_QUITE_EVENT);
    }
    if (test_manage_thread)
    {
        main_send_event_to_test_manage(MANAGE_QUITE_EVENT);
    }
    sleep(2);
    if (tty_fd)
        close(tty_fd);
    fflush(pLogFile); /*将数据同步至ROM*/
    file_reorg_data(pLogFile);
    file_close(pLogFile); /*关闭文件*/
    file_close(presultFile);
    remove_pid(pid_file);
    if (net_inter_signal_fd[0])
        close(net_inter_signal_fd[0]);
    if (net_inter_signal_fd[1])
        close(net_inter_signal_fd[1]);
    if (test_manage_signal_fd[0])
        close(test_manage_signal_fd[0]);
    if (test_manage_signal_fd[1])
        close(test_manage_signal_fd[1]);
    if (tty_receive_signal_fd[0])
        close(tty_receive_signal_fd[0]);
    if (tty_receive_signal_fd[1])
        close(tty_receive_signal_fd[1]);
    if (manage_net_signal_fd[0])
        close(manage_net_signal_fd[0]);
    if (manage_net_signal_fd[1])
        close(manage_net_signal_fd[1]);

    func_set_gpio_sta(test->tester_power_path, 0);
    func_free_test_memory(test);
    free(test);
    free(frame_queue);
    usleep(20000);
    exit(0);
}
